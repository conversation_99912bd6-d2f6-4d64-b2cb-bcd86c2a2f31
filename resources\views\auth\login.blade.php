<x-guest-layout>
    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success mb-3" role="alert">
            {{ session('status') }}
        </div>
    @endif

    <form class="form-horizontal form-material" id="loginform" method="POST" action="{{ route('login') }}">
        @csrf
        <h3 class="box-title m-b-20">Sign In</h3>

        <!-- Email Address -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('email') is-invalid @enderror"
                       type="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       autofocus
                       autocomplete="username"
                       placeholder="Username">
                @error('email')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Password -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('password') is-invalid @enderror"
                       type="password"
                       name="password"
                       required
                       autocomplete="current-password"
                       placeholder="Password">
                @error('password')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="form-group">
            <div class="col-md-12">
                <div class="checkbox checkbox-primary pull-left p-t-0">
                    <input id="checkbox-signup" type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }}>
                    <label for="checkbox-signup"> Remember me </label>
                </div>
                @if (Route::has('password.request'))
                    <a href="javascript:void(0)" id="to-recover" class="text-dark pull-right">
                        <i class="fa fa-lock m-r-5"></i> Forgot pwd?
                    </a>
                @endif
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center m-t-20">
            <div class="col-xs-12">
                <button class="btn btn-info btn-lg btn-block text-uppercase waves-effect waves-light" type="submit">
                    Log In
                </button>
            </div>
        </div>

        <!-- Social Login -->
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 m-t-10 text-center">
                <div class="social">
                    <a href="javascript:void(0)" class="btn btn-facebook" data-toggle="tooltip" title="Login with Facebook">
                        <i aria-hidden="true" class="fa fa-facebook"></i>
                    </a>
                    <a href="javascript:void(0)" class="btn btn-googleplus" data-toggle="tooltip" title="Login with Google">
                        <i aria-hidden="true" class="fa fa-google-plus"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Register Link -->
        <div class="form-group m-b-0">
            <div class="col-sm-12 text-center">
                <p>Don't have an account? <a href="{{ route('register') }}" class="text-primary m-l-5"><b>Sign Up</b></a></p>
            </div>
        </div>
    </form>

    <!-- Forgot Password Form -->
    <form class="form-horizontal" id="recoverform" method="POST" action="{{ route('password.email') }}" style="display: none;">
        @csrf
        <div class="form-group">
            <div class="col-xs-12">
                <h3>Recover Password</h3>
                <p class="text-muted">Enter your Email and instructions will be sent to you!</p>
            </div>
        </div>
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control" type="email" name="email" required placeholder="Email">
            </div>
        </div>
        <div class="form-group text-center m-t-20">
            <div class="col-xs-12">
                <button class="btn btn-primary btn-lg btn-block text-uppercase waves-effect waves-light" type="submit">
                    Reset
                </button>
            </div>
        </div>
        <div class="form-group m-b-0">
            <div class="col-sm-12 text-center">
                <p><a href="javascript:void(0)" id="to-login" class="text-primary m-l-5"><b>Back to Login</b></a></p>
            </div>
        </div>
    </form>
</x-guest-layout>
