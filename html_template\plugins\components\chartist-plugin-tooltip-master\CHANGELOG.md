# Chartist Plugin Tooltip Changelog

### master

### 0.0.11
* [BUGFIX] Tooltips now working properly on Firefox
* [ENHANCEMENT] Added custom tooltip using options.tooltipFnc

### 0.0.10
* [BUGFIX] Set currency default to `undefined`. Fixes issue in chartist.js 0.7.4.

### 0.0.9
* [BUGFIX] Fixed new pie chart label mechanism

### 0.0.8
* [BUGFIX] Removed Ember reference
* [ENHANCEMENT] For pie chart tooltips also take the labels into account if no meta is given
