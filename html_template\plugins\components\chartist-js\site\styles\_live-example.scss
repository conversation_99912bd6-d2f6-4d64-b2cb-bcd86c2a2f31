.live-example {
  @include grid-row(nest);

  margin-bottom: rem-calc(nth($modular-scale, 4));

  &.edit-mode {
    .chart {
      @media #{$large-up} {
        @include grid-column(12);
      }
    }

    .live-code {
      @media #{$large-up} {
        @include grid-column(12);
      }

      .introduction {
        display: none;
      }

      .editor-area {
        display: block;
      }
    }
  }

  .live-example-header {
    @include grid-column(12);
  }

  .chart {
    @include grid-column(12);

    @media #{$large-up} {
      @include grid-column(8);
    }
  }

  .live-code {
    @include grid-column(12);

    .editor-area {
      display: none;
    }

    @media #{$large-up} {
      @include grid-column(4);
    }
  }

  .ct-negative-labels {
    .ct-label {
      fill: rgba(255, 255, 255, 0.8);
    }
  }
}
