<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f8d2eb18-6977-45a7-8b51-5df4dc843151" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app/Http/Controllers/Auth/RegisteredUserController.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Http/Controllers/Auth/RegisteredUserController.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/views/auth/login.blade.php" beforeDir="false" afterPath="$PROJECT_DIR$/resources/views/auth/login.blade.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/views/auth/register.blade.php" beforeDir="false" afterPath="$PROJECT_DIR$/resources/views/auth/register.blade.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/views/layouts/guest.blade.php" beforeDir="false" afterPath="$PROJECT_DIR$/resources/views/layouts/guest.blade.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/views/welcome.blade.php" beforeDir="false" afterPath="$PROJECT_DIR$/resources/views/welcome.blade.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/web.php" beforeDir="false" afterPath="$PROJECT_DIR$/routes/web.php" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="E:\xampp\php\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/league/config" />
      <path value="$PROJECT_DIR$/vendor/league/uri" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/staabm/side-effects-detector" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/vendor/laravel/breeze" />
      <path value="$PROJECT_DIR$/vendor/laravel/pail" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/vendor/laravel/tinker" />
    </include_path>
  </component>
  <component name="ProjectId" id="2zeYiV1nFCHg37CI7e8mCT3fyB7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "E:/xampp/htdocs/laravel_twelve_starter",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="phpunit.xml" type="PHPUnitRunConfigurationType" factoryName="PHPUnit">
      <TestRunner scope="XML" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f8d2eb18-6977-45a7-8b51-5df4dc843151" name="Changes" comment="" />
      <created>1752093132798</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752093132798</updated>
      <workItem from="1752093134541" duration="2838000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>