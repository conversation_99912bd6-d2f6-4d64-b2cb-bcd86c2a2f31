.module-member {
  margin-bottom: 2em;
  background-color: mix(black, $color-white, 5%);
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;

  .member-header {
    position: relative;

    .api-code-button {
      @include button();
      text-transform: uppercase;
      width: calc(100% + 20px);
      margin: 0 -10px;


      @media #{$medium-up} {
        position: absolute;
        width: auto;
        margin: 0;
        top: 0;
        right: -10px;
        padding: 0 10px;
        height: 100%;
      }
    }
  }

  .description {
    margin-top: 1em;
    margin-bottom: 2em;

    strong {
      display: inline-block;
      padding-top: 1em;
      padding-bottom: 0.5em;
    }
  }

  .member-title {
    background: #453D3F;
    padding: 10px;
    margin: -10px -10px 0 -10px;
    color: lighten($color-white, 10%);
    text-transform: none;
  }

  .api-example {
    margin-top: 2em;
  }

  .param {
    margin-bottom: 0.5em;

    > code {
      font-weight: bold;
    }
  }

  .param-type {

    > code {
      background-color: rgba(255, 255, 255, 0.2);
      padding: 0.1em 0.2em;
    }

    &:after {
      content: "╱";
    }

    &:last-of-type {
      &:after {
        content: none;
      }
    }
  }

  pre {
    margin: 0 -10px;
  }
}
