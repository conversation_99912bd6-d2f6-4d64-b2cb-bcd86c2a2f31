<?php

use App\Http\Controllers\ProfileController;
use App\Models\User;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Email validation API route for jQuery remote validation
Route::get('/api/check-email', function (Request $request) {
    $email = $request->query('email');

    // Validate email format first
    if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return response()->json(false);
    }

    // Check if email already exists
    $exists = User::where('email', $email)->exists();

    // Return true if email is available (doesn't exist), false if taken
    return response()->json(!$exists);
})->name('api.check-email');



Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
