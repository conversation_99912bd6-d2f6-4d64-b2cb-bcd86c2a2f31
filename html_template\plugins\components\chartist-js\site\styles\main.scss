@import "settings/chartist-docs";
@import "settings/foundation";
@import "settings/font-awesome-variables";

@import "modules";

@import "../../src/styles/settings/chartist-settings";
@import "../../src/styles/chartist";

@import "compass-mixins/lib/compass/css3";
@import "foundation/scss/normalize";
@import "foundation/scss/foundation";
@import "base";
@import "landing";
@import "highlight";
@import "code-mirror-theme";
@import "api-doc";
@import "live-example";
@import "example-charts";

.button {
  text-transform: uppercase;
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
}

.invisible {
  display: block !important;
  max-height: 0;
  overflow: hidden;
  visibility: hidden;
}

.text-clipping {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.hint {
  position: relative;
  margin-top: rem-calc(nth($modular-scale, 5));
  margin-bottom: rem-calc(nth($modular-scale, 5));
  padding: rem-calc(nth($modular-scale, 5));
  background: $color-gray;
  color: lighten($color-white, 10%);

  > .title {
    font-size: rem-calc(nth($modular-scale, 4));
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: lighten($color-black, 20%);
  }

  a {
    color: $color-yellow;
    &:hover {
      color: lighten($color-yellow, 10%);
    }
  }

  &[class*="hint-"] {
    padding-left: 5.2rem;

    &.hint-cross-browser {
      @include icon($fa-var-arrows-alt, 3rem);
    }

    &:before {
      position: absolute;
      left: rem-calc(nth($modular-scale, 5));
      top: rem-calc(nth($modular-scale, 5));
      font-size: 3rem;
      color: lighten($color-black, 5%);
    }
  }
}

code {
  font-family: "Source Code Pro", "Courier New", monospace !important;
  word-wrap: break-word;
}

.sticky.contain-to-grid {
  box-shadow: 0 3px 0 rgba(0, 0, 0, 0.3);

  &.fixed, .page-content & {
    .top-bar .name h1 a:before {
      content: "";
      display: inline-block;
      width: 34px;
      height: 34px;
      margin-top: -4px;
      margin-right: 10px;
      vertical-align: middle;
      border-radius: 50%;
      border: 2px solid #F4C63D;
      background-size: 32px 32px;
      background: #F4C63D url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjAiIHk9IjAiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMzIgMzIiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxnIGlkPSJoZWFkIj48cGF0aCBmaWxsPSIjNzU1NDNCIiBkPSJNMTMuOCAyMC42czEuNiA1LTMuOSA1bDQuOSAxLjUgNC00LjItNS0yLjN6Ii8+PHBhdGggZmlsbD0iI0IyNDk0OSIgZD0iTTE3LjEgMjVzLTQuMS0xLjUtNS45IDEuMWwzLjcuOSAyLjItMnoiLz48cGF0aCBmaWxsPSIjRDM5Qzc2IiBkPSJNOS45IDI1LjRjMi4xIDEuMSA1IC42IDYgMCAxLjUtLjggMS41LTMgMS4xLTMuMi0uMi0uMS0yLjIgMS4zLTUuOS4xbC40LTMuMy01LjktLjJMOS43IDguNHM3LTIuOSAxNCAwVjE5Yy40LS4xLjgtLjIgMS4xIDAgLjkuMyAxLjEgMS40LjYgMi41LS40LjctMSAxLjMtMS43IDEuNHY2LjJzLTcuMiA0LjgtMTUuMSAyLjFjMCAwLTIuNS0uOC0yLjMtMy40IDAtLjItLjItMS45IDMuNi0yLjR6Ii8+PGcgZmlsbD0iIzYzNDEyOCI+PHBhdGggZD0iTTE1LjEgMTQuNGMwIC41LjkgMSAxLjkgMSAxLjEgMCAxLjktLjQgMS45LTEgMC0uNS0uOS0xLTEuOS0xLTEgMC0xLjkuNS0xLjkgMXpNOS43IDE0LjRjMCAuNS44IDEgMS45IDEgMSAwIDEuOS0uNCAxLjktMXMtLjgtMS0xLjktMWMtMS4xLjEtMS45LjUtMS45IDF6Ii8+PC9nPjxwYXRoIGZpbGw9IiMyMzFGMjAiIGQ9Ik0yMy44IDEwLjVWNmMuMS0zLjYgMS40LTUuMS40LTUuMy0xLS4yLTMuMi0uNi03LjgtLjYtNC4xIDAtNy4yLjUtOC4yLjZDNy4zIDEgOSAzLjkgOS4zIDZjLjEgMSAuMSAyLjUuMSAzLjgtMy4zLjUtNS4zIDEuNy01LjcgMi44aDI2LjZzLS4zLTEuMS02LjUtMi4xeiIvPjxwYXRoIGZpbGw9IiMzNTM0MzMiIGQ9Ik0xMi44IDIyLjRjMy4xIDIuOCAxMC40IDIuMyAxMC45LTIuMS4xLTEuMy0uNS0uMy0xLjEuMS0uOS42LTIuNS42LTMuNCAwLTEuNS0xLTQuMS00LjEtNi40LTEuOC0yLjQtMi4zLTUgLjgtNi41IDEuOC0uOS42LTIuNi42LTMuNSAwLS42LS40LTEuMi0xLjMtMS4xLS4xLjUgNC40IDcuOSA0LjkgMTEuMSAyLjF6Ii8+PC9nPjwvc3ZnPg==") no-repeat center;
    }
  }
}

.ct-chart {
  margin-bottom: 1rem;
}

.ct-chart-pie .ct-label, .ct-chart-donut .ct-label {
  font-size: 1em;

  @media #{$small-only} {
    fill: rgba(255, 255, 255, 0.8);
  }
}

.main {
  @include grid-row();

  @media #{$medium-up} {
    margin-top: rem-calc(nth($modular-scale, 8));
  }

  > header {
    @include grid-column(12);
    margin-top: rem-calc(nth($modular-scale, 5));

    .page-content & {
      margin-bottom: rem-calc(nth($modular-scale, 8));
    }
  }

  > .side-navigation {
    display: none;

    @media #{$medium-up} {
      display: block;
      @include grid-column(4);

      > nav {
        max-width: 250px;

        > .side-nav {
          background-color: mix(black, $color-white, 5%);
          box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);

          > .heading {
            font-size: rem-calc(nth($modular-scale, 4));
            text-transform: uppercase;
            font-weight: 600;
            margin-top: rem-calc(nth($modular-scale, 7));
            margin-bottom: rem-calc(nth($modular-scale, 4));
            color: $color-black;

            &:first-child {
              margin-top: 0;
            }
          }

          > li:not(.heading) > a {

            &:before {
              content: "\2013\00A0";
            }
          }

          > li a:not(.button) {
            padding: 0;

            &:hover {
              background: none;
            }
          }
        }
      }
    }
  }

  > .content {
    @include grid-column(12);

    @media #{$medium-up} {
      @include grid-column(8);
    }

    > .documentation-section {
      @include grid-row(nest);

      margin-top: rem-calc(nth($modular-scale, 4));
      margin-bottom: rem-calc(nth($modular-scale, 6));

      &:first-child {
        margin-top: 0;
      }

      @media #{$medium-up} {
        margin-top: rem-calc(nth($modular-scale, 6));
        margin-bottom: rem-calc(nth($modular-scale, 8));
      }

      > header {
        @include grid-column(12);
      }

      > .content {
        @include grid-column(12);

        @media #{$medium-up} {
          @include grid-column(8);
        }

        @media #{$large-up} {
          @include grid-column(9);
        }

        .sub-section {
          margin-bottom: 3rem;
        }

        > .ct-chart {
          &.dark {
            background-color: $color-gray;
          }
        }
      }

      > .side-notes {
        @include grid-column(12);

        @media #{$medium-up} {
          @include grid-column(4);
        }

        @media #{$large-up} {
          @include grid-column(3);
        }
      }

      &.full {
        > .content {
          @media #{$medium-up} {
            @include grid-column(12);
          }
        }

        > .side-notes {
          @media #{$medium-up} {
            @include grid-column(12);
          }
        }
      }
    }

    > ul.example-gallery {
      @include block-grid(1);

      @media #{$medium-up} {
        @include block-grid(2);
      }
    }
  }

  .full & {
    > .side-navigation {
      @include grid-column(12);
    }

    > .content {
      @include grid-column(12);
    }
  }
}

@include keyframes(dashoffset) {
  0% {
    stroke-dashoffset: 0px;
  }

  100% {
    stroke-dashoffset: -20px;
  }
}

@include keyframes(bouncing-stroke) {
  0% {
    stroke-width: 5px;
  }

  50% {
    stroke-width: 10px;
  }

  100% {
    stroke-width: 5px;
  }
}

@include keyframes(exploding-stroke) {
  0% {
    stroke-width: 2px;
    opacity: 1;
  }

  100% {
    stroke-width: 20px;
    opacity: 0;
  }
}

