---
layout: landing
page-class: full
title: Chartist - Simple responsive charts
description: Create responsive, scalable and good looking charts with chartist.js.
preventIndent: true
---
<p>You may think that this is just yet an other charting library. But Chartist.js is the product of
a community that was disappointed about the abilities provided by other charting libraries. Of course there are hundreds
of other great charting libraries but after using them there were always tweaks you would have wished for that were not
  included.</p>

<h3>Highly customizable responsive charts</h3>
<ul class="example-gallery">
  {{#each page.gallery-examples}}
    <li>
      {{> example-chart }}
    </li>
  {{/each}}
</ul>

<h3>Facts about Chartist</h3>
<p>The following facts should give you an overview why to choose Chartists as your front-end chart generator:</p>
<ul class="list">
  <li>Simple handling while using convention over configuration</li>
  <li>Great flexibility while using clear separation of concerns (Style with CSS & control with JS)</li>
  <li>Usage of SVG (Yes! SVG is the future of illustration in web!)</li>
  <li>Fully responsive and DPI independent</li>
  <li>Responsive configuration with media queries</li>
  <li>Fully built and customizable with Sass</li>
</ul>

{{#each page.sections}}
  {{>section}}
{{/each}}
