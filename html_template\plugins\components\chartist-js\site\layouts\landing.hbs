---
layout: default
---
<section class="page-landing {{page-class}}">
  <header class="landing-page-header">
    <div class="introduction-title">Chartist.js
      <small> Simple responsive charts</small>
    </div>
    <div class="limiter">
      <figure>
        <svg id="chartist-guy" data-svg-src="images/chartist-guy.svg"></svg>
      </figure>
    </div>
    <div class="library-statement">
      Only <span class="important">10KB</span> (Gzip) with <span class="important">no dependencies</span>!
    </div>
    <div class="call-to-action">
      <a class="button large download" href="https://github.com/gionkunz/chartist-js/tree/develop/dist" target="_blank" data-icon="&#xf019;">Download</a>
      <a class="button large contribute" href="https://github.com/gionkunz/chartist-js" target="_blank" data-icon="&#xf09b;">Contribute</a>
    </div>
  </header>

  {{> navigation }}

  <article class="main" role="main">
    <header>
      <h2>{{title}}</h2>
    </header>
    <div class="content">
      {{> body  }}
    </div>
  </article>
</section>
