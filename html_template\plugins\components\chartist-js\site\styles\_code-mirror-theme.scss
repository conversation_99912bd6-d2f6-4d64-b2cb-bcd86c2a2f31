.CodeMirror-scroll {
  overflow: auto !important;
}

.cm-s-chartist.CodeMirror {
  background: $color-black;
  color: lighten($color-white, 10%);
  padding: rem-calc(nth($modular-scale, 5));
  font-family: "Source Code Pro", "Courier New", monospace !important;
  line-height: 1.4;
  word-wrap: break-word;
  height: auto;
  margin-bottom: rem-calc(nth($modular-scale, 5));
}

.cm-s-chartist div.<PERSON>Mirror-selected {
  background: darken($color-black, 10%) !important;
}

.cm-s-chartist .CodeMirror-gutters {
  background: #f7f7f7;
  border-right: 0px;
}

.cm-s-chartist .CodeMirror-guttermarker {
  color: $color-grapefruit;
}

.cm-s-chartist .CodeMirror-guttermarker-subtle {
  color: darken($color-black, 10%);
}

.cm-s-chartist .CodeMirror-linenumber {
  color: darken($color-black, 20%);
}

.cm-s-chartist .CodeMirror-cursor {
  border-left: 2px solid lighten($color-black, 10%) !important;
}

.cm-s-chartist span.cm-comment {
  color: lighten($color-black, 20%);
}

.cm-s-chartist span.cm-atom {
  color: $color-yellow;
}

.cm-s-chartist span.cm-number {
  color: $color-yellow;
}

.cm-s-chartist span.cm-property, .cm-s-3024-day span.cm-attribute {
  color: lighten($color-white, 10%);
}

.cm-s-chartist span.cm-keyword {
  color: $color-grapefruit;
  font-weight: bold;
}

.cm-s-chartist span.cm-string {
  color: $color-yellow;
}

.cm-s-chartist span.cm-variable {
  color: lighten($color-white, 10%);
}

.cm-s-chartist span.cm-variable-2 {
  color: lighten($color-grapefruit, 20%);
}

.cm-s-chartist span.cm-def {
  color: lighten($color-grapefruit, 20%);
}

.cm-s-chartist span.cm-bracket {
  color: #3a3432;
}

.cm-s-chartist span.cm-tag {
  color: $color-grapefruit;
  font-weight: bold;
}

.cm-s-chartist span.cm-link {
  color: $color-yellow;
}

.cm-s-chartist span.cm-error {
  background-color: $color-grapefruit;
  color: $color-black;
}

.cm-s-chartist .CodeMirror-activeline-background {
  background: #e8f2ff !important;
}

.cm-s-chartist .CodeMirror-matchingbracket {
  text-decoration: underline;
  color: white !important;
}
