$icon-font-name: "FontAwesome" !default;
$icon-location: before !default;
$icon-vertical-align: baseline !default;

$icon-attr-name: data-icon !default;
$icon-include-classes: true !default;

@mixin icon($icon: "", $icon-size: 1em, $icon-location: $icon-location, $icon-font-name: $icon-font-name, $icon-vertical-align: $icon-vertical-align) {
  &:#{$icon-location} {
    content: $icon;
    display: inline-block;
    vertical-align: $icon-vertical-align;
    font-family: $icon-font-name;
    font-style: normal;
    font-weight: normal;
    font-size: $icon-size;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    // In usage with google font loader we will hide and unhide based on status class on html element
    .wf-loading & {
      visibility: hidden;
    }
  }
}

@if ($icon-include-classes) {
  [#{$icon-attr-name}] {
    @include icon(attr(#{$icon-attr-name}));
  }
}