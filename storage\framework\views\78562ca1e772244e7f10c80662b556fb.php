<?php $__env->startPush('css'); ?>
<style>
/* Register Form Validation Styles */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
    position: relative;
    z-index: 2;
}

.form-control + .text-danger {
    margin-top: 5px;
    margin-bottom: 10px;
}

.form-check + .text-danger {
    margin-top: 5px;
    margin-left: 0;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <form class="form-horizontal form-material" id="registerform" method="POST" action="<?php echo e(route('register')); ?>">
        <?php echo csrf_field(); ?>
        <h3 class="box-title m-b-20">Sign Up</h3>

        <!-- Name -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="text"
                       name="name"
                       value="<?php echo e(old('name')); ?>"
                       autofocus
                       autocomplete="name"
                       placeholder="Name">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Email Address -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="email"
                       name="email"
                       value="<?php echo e(old('email')); ?>"
                       autocomplete="username"
                       placeholder="Email">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="password"
                       name="password"
                       autocomplete="new-password"
                       placeholder="Password">
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Confirm Password -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="password"
                       name="password_confirmation"
                       autocomplete="new-password"
                       placeholder="Confirm Password">
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Terms Agreement -->
        <div class="form-group mb-3">
            <div class="col-12">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="checkbox-signup" name="terms">
                    <label class="form-check-label" for="checkbox-signup">
                        I agree to all <a href="#" class="text-primary">Terms</a>
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center mt-3 mb-3">
            <div class="col-12">
                <button class="btn btn-info btn-lg w-100 text-uppercase waves-effect waves-light" type="submit" id="register-btn">
                    Sign Up
                </button>
            </div>
        </div>

        <!-- Login Link -->
        <div class="form-group mb-0">
            <div class="col-12 text-center">
                <p>Already have an account? <a href="<?php echo e(route('login')); ?>" class="text-primary ms-1"><b>Sign In</b></a></p>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    $('#registerform').validate({
        rules: {
            name: {
                required: true,
                minlength: 2
            },
            email: {
                required: true,
                email: true,
                remote: "/api/check-email"
            },
            password: {
                required: true,
                minlength: 8
            },
            password_confirmation: {
                required: true,
                equalTo: "input[name='password']"
            },
            terms: {
                required: true
            }
        },
        messages: {
            name: {
                required: "Name is required",
                minlength: "Name must be at least 2 characters"
            },
            email: {
                required: "Email is required",
                email: "Please enter a valid email",
                remote: "Email already exists"
            },
            password: {
                required: "Password is required",
                minlength: "Password must be at least 8 characters"
            },
            password_confirmation: {
                required: "Please confirm password",
                equalTo: "Passwords do not match"
            },
            terms: {
                required: "You must agree to the terms"
            }
        },
        errorClass: 'text-danger',
        errorElement: 'div',
        errorPlacement: function(error, element) {
            if (element.attr('type') === 'checkbox') {
                error.insertAfter(element.closest('.form-check'));
            } else {
                error.insertAfter(element);
            }
        },
        highlight: function(element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function(element) {
            $(element).removeClass('is-invalid').addClass('is-valid');
        },
        submitHandler: function(form) {
            $('#register-btn').html('<i class="fas fa-spinner fa-spin"></i> Creating Account...').prop('disabled', true);
            form.submit();
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.guest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\xampp\htdocs\laravel_twelve_starter\resources\views/auth/register.blade.php ENDPATH**/ ?>