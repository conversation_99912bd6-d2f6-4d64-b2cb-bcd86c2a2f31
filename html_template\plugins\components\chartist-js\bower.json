{"name": "chartist", "main": ["./dist/chartist.min.js", "./dist/chartist.min.css"], "devDependencies": {"snap.svg": "~0.3.0", "foundation": "~5.5.1", "highlightjs": "~8.4.0", "compass-mixins": "~1.0.2", "codemirror": "~4.12.0", "base64": "~0.3.0", "chartist-plugin-pointlabels": "~0.0.4", "chartist-plugin-sketchy": "~0.0.2", "chartist-plugin-accessibility": "~0.0.2", "chartist-plugin-tooltip": "~0.0.8", "chartist-plugin-axistitle": "~0.0.1", "chartist-plugin-threshold": "~0.0.1", "chartist-plugin-fill-donut": "~0.0.1", "chartist-plugin-zoom": "~0.2.1", "matchMedia": "~0.2.0"}, "ignore": [".*", "Gruntfile.js", "tasks", "package.json", "node_modules", "public", "site", "src", "test"], "resolutions": {"chartist": "~0.9.0"}}