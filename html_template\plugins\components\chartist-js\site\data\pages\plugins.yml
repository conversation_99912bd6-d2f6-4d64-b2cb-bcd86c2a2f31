sections:
  - title: Overview
    level: 3
    items:
      - type: sub-section
        data:
          title: What's a plugin?
          level: 4
          items:
            - type: text
              data:
                text: >
                        Plugins allow you to extend the basic functionality of your charts. You can develop your own
                        plugins or use plugins that others have already developed.

      - type: sub-section
        data:
          title: How to use plugins
          level: 4
          items:
            - type: text
              data:
                text: >
                        Once you have included a plugin in your project you can use it in your chart by specifying it explicitly
                        in the plugins section of your chart configuration. Check the <a href="#available-plugins">List of plugins</a>
                        section to see what plugins you can use.

            - type: code-snippet
              data:
                id: plugin-include
                lang: js

            - type: hint
              data:
                title: Order of specification
                text: >
                        Plugins are chainable and the order of specification in the plugins array of your configuration
                        is important for the end result.

            - type: text
              data:
                text: >
                        Chartist.js expects an array of plugin functions to be present in the <code>plugins</code> array
                        of the chart configuration. Usually plugins should be written as function factories so you
                        can pass additional parameters and options to the factory which is creating the plugin function and returns it.

  - title: Available plugins
    level: 3
    items:
      - type: text
        data:
          text: >
                  Here you can find a list of known plugins. Usually plugins should be available from both Bower and NPM for
                  installation. If you have developed your own plugin but can't find it here, you should create a
                  <a href="https://github.com/gionkunz/chartist-js/blob/develop/site/data/pages/examples.yml" target="_blank">pull request
                  for this page</a> and add your plugin to the list.

      - type: sub-section
        data:
          title: Accessibility Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        285 million people are estimated to be visually impaired worldwide: 39 million are blind and 246 have low vision.
                        We should stop the discrimination against our fellow human and, by fairly little effort and following best
                        practices, make our content on the web accessible.
            - type: text
              data:
                text: >
                        The accessibility plugin makes your chart accessible for blind people. It automatically generates
                        visually hidden accessibility tables that allow you to describe your chart for blind people easily.
                        This plugin was tested with NVDA and JAWS, and provides all necessary things for building an accessible chart.
            - type: text
              data:
                text: >
                        By simply including this plugin with default configurations you will already make your charts
                        accessible to blind people! If you put in 5 minutes effort in customizing the configuration with
                        meaningful content, you will actually even make them enjoy your charts!
            - type: live-example
              data:
                id: example-plugin-accessibility
                classes: ct-golden-section
                intro: >
                         Creating an accessible chart that shows some fiscal year figures. This example overrides the visuallyHiddenStyles
                         configuration property so you can actually see the "invisible" table.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Gion Kunz
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/gionkunz/chartist-plugin-accessibility" target="_blank">chartist-plugin-accessibility</a>'

      - type: sub-section
        data:
          title: Tooltip Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The tooltip plugin makes it super simple to add tooltips to most of your charts which can often
                        increase it's usability.
            - type: text
              data:
                text: >
                        By simply including this plugin with default configurations you will already make your charts
                        get some tooltips showing.
            - type: live-example
              data:
                id: example-plugin-tooltip
                classes: ct-golden-section
                intro: >
                         Creating a simple line chart showing how the tooltips work.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Markus Padourek
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/Globegitter/chartist-plugin-tooltip" target="_blank">chartist-plugin-tooltip</a>'

      - type: sub-section
        data:
          title: Point Label Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The point label plugin can be used if you like to add simple labels on top of your data points
                        on line charts. This is usefull if you'd like the user to see the exact values of the data without
                        any additional interaction.
            - type: live-example
              data:
                id: example-plugin-pointlabel
                classes: ct-golden-section
                intro: >
                         The point label plugin draws labels on top of your line chart points.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Gion Kunz
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/gionkunz/chartist-plugin-pointlabels" target="_blank">chartist-plugin-pointlabels</a>'

      - type: sub-section
        data:
          title: Sketchy Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The sketchy plugin makes your charts look like they have been drawn by hand. This plugin makes use
                        of SVG filters and works on IE10+, Safari 7+ and Android 4.4+. Also note that SVG filters are not
                        very performant and they will eat your users mobile battery quickly for sure.
            - type: live-example
              data:
                id: example-plugin-sketchy
                classes: ct-golden-section
                intro: >
                         Create beautiful hand drawn charts using the Chartist Sketchy plugin.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Gion Kunz
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/gionkunz/chartist-plugin-sketchy" target="_blank">chartist-plugin-sketchy</a>'
      - type: sub-section
        data:
          title: Axis Title Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The axis title plugin allows you to add simple titles to your axes.
            - type: live-example
              data:
                id: example-plugin-axistitle
                classes: ct-golden-section
                intro: >
                          While not a core functionality, this plugin makes it absolutely easy to add axis titles to your chart.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Alex Stanbury
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/alexstanbury/chartist-plugin-axistitle" target="_blank">chartist-plugin-axistitle</a>'
      - type: sub-section
        data:
          title: Threshold Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        This Chartist plugin can be used to divide your Line or Bar chart with a threshold.
            - type: live-example
              data:
                id: example-plugin-threshold
                classes: ct-golden-section
                intro: >
                          Everything above and below the
                          threshold will be tagged with a special class, in order for your to apply different styling where appropriate.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Gion Kunz
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/gionkunz/chartist-plugin-threshold" target="_blank">chartist-plugin-threshold</a>'
      - type: sub-section
        data:
          title: FillDonut Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        Let animated donuts look filled and provide options for append labels and html to the donut chart.
                        This plugin draw the donut a second time but without delay and animation, so it animation will overlaps and looks like the fill the donut.
                        Also your a possible to add multiple html-labels to the donut on different positions.
            - type: live-example
              data:
                id: example-plugin-fill-donut
                classes: ct-golden-section
                intro: >
                          Let animated donuts look filled and provide options for append labels and html to the donut chart.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Moxx
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/moxx/chartist-plugin-fill-donut" target="_blank">chartist-plugin-fill-donut</a>'
      - type: sub-section
        data:
          title: Zoom Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The zoom plugin allows you to zoom into charts.
            - type: live-example
              data:
                id: example-plugin-zoom
                classes: ct-golden-section
                intro: >
                          Use the left mouse button to drag a zoom box. The plugin provides a callback that can be used to reset the zoom.
            - type: table
              data:
                rows:
                  -
                    - '<strong>Author:</strong>'
                    - Hannes Kamecke
                  -
                    - '<strong>Link:</strong>'
                    - '<a href="https://github.com/hansmaad/chartist-plugin-zoom" target="_blank">chartist-plugin-zoom</a>'
  - title: Develop a plugin
    level: 3
    items:

      - type: text
        data:
          text: >
                  Plugins are functions that will be called for each chart that is created with the plugin enabled
                  (specified in the <code>plugins</code> configuration of a chart). The plugin function will be called with one
                  argument which is the chart that is registering itself for the plugin. If you wish to use some additional
                  parameters or configuration for your plugin initialization, it's recommended to use a function factory.
                  You can check the example plugin for an implementation using a function factory.

      - type: code-snippet
        data:
          id: plugin-signature
          lang: js

      - type: text
        data:
          text: >
                  From the chart object <code>options</code>, <code>svg</code> (root SVG element) and the
                  <code>eventEmitter</code> can be used to manipulate the behaviour of the chart. It's the
                  responsibility of the plugin to decide if it should be activated on a given chart (i.e. by checking
                  the chart type <code>chart instanceof Chartist.Line</code> etc.).

      - type: text
        data:
          text: >
                  It's recommended to use the events of Chartist.js (like <code>draw</code>) to manipulate the
                  underlying elements. Using the events, plugins can chain up in a natural way and work independently
                   on extending the functionality of the chart.

      - type: text
        data:
          text: >
                  Plugins should contain their own default settings and use <code>Chartist.extend</code> to override
                  the settings specified in the options passed to the plugin factory function. Using
                  the <code>optionsProvider</code> of the chart object one could also implement functioanlity based on
                  the chart configuration as well as responsive configuration.

      - type: sub-section
        data:
          title: Example Plugin
          level: 4
          items:
            - type: text
              data:
                text: >
                        The following code shows an example plugin that is also available for download and installation.
                        You can also use the <a href="https://github.com/gionkunz/chartist-plugin-pointlabels" target="_blank">repository
                        of the example plugin</a> to start your own awesome Chartist.js plugin.

      - type: code-snippet
        data:
          id: plugin-example
          lang: js
