<!DOCTYPE html>  
<!--[if lt IE 7]>
<html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>
<html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>
<html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js"> <!--<![endif]-->
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>{{ title }}</title>
  <meta name="description" content="{{ description }}">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <!-- build:css(site) styles/vendor.css -->
  <link rel="stylesheet" href="bower_components/codemirror/lib/codemirror.css" />
  <link rel="stylesheet" href="bower_components/foundation/css/normalize.css"/>
  <!-- endbuild -->
  <!-- build:css({.tmp,site}) styles/main.css -->
  <link rel="stylesheet" href="styles/main.css">
  <!-- endbuild -->
</head>
<body>

{{> body  }}

<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-53685926-1', 'auto');
  ga('require', 'displayfeatures');
  ga('send', 'pageview');
</script>

<!-- build:js({site,src}) scripts/all.js -->
<!-- Vendor scripts-->
<script src="bower_components/modernizr/modernizr.js"></script>
<script src="bower_components/jquery/dist/jquery.js"></script>
<script src="bower_components/snap.svg/dist/snap.svg-min.js"></script>
<script src="bower_components/fastclick/lib/fastclick.js"></script>
<script src="bower_components/jquery.cookie/jquery.cookie.js"></script>
<script src="bower_components/jquery-placeholder/jquery.placeholder.js"></script>
<script src="bower_components/foundation/js/foundation.js"></script>
<script src="bower_components/highlightjs/highlight.pack.js"></script>
<script src="bower_components/codemirror/lib/codemirror.js"></script>
<script src="bower_components/base64/base64.js"></script>
<script src="bower_components/codemirror/mode/javascript/javascript.js"></script>
<script src="bower_components/matchMedia/matchMedia.js"></script>
<script src="bower_components/matchMedia/matchMedia.addListener.js"></script>

<!-- Chartist scripts -->
<script src="scripts/core.js"></script>
<script src="scripts/interpolation.js"></script>
<script src="scripts/event.js"></script>
<script src="scripts/class.js"></script>
<script src="scripts/base.js"></script>
<script src="scripts/svg.js"></script>
<script src="scripts/svg-path.js"></script>
<script src="scripts/axes/axis.js"></script>
<script src="scripts/axes/auto-scale-axis.js"></script>
<script src="scripts/axes/fixed-scale-axis.js"></script>
<script src="scripts/axes/step-axis.js"></script>
<script src="scripts/charts/line.js"></script>
<script src="scripts/charts/bar.js"></script>
<script src="scripts/charts/pie.js"></script>

<!-- Chartist plugins -->
<script src="bower_components/chartist-plugin-pointlabels/dist/chartist-plugin-pointlabels.js"></script>
<script src="bower_components/chartist-plugin-sketchy/dist/chartist-plugin-sketchy.js"></script>
<script src="bower_components/chartist-plugin-accessibility/dist/chartist-plugin-accessibility.js"></script>
<script src="bower_components/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.js"></script>
<script src="bower_components/chartist-plugin-axistitle/dist/chartist-plugin-axistitle.js"></script>
<script src="bower_components/chartist-plugin-threshold/dist/chartist-plugin-threshold.js"></script>
<script src="bower_components/chartist-plugin-fill-donut/dist/chartist-plugin-fill-donut.js"></script>
<script src="bower_components/chartist-plugin-zoom/dist/chartist-plugin-zoom.js"></script>

<!-- Chartist site scripts -->
<script src="scripts/main.js"></script>
<script src="scripts/chartist-guy.js"></script>
<!-- endbuild -->

<!-- Take web fonts off the critical path but risk FOUT -->
<script type="text/javascript">
  WebFontConfig = {
    google: { families: ['Oxygen:400,300,700', 'Source+Code+Pro:400,700'] },
    custom: {
      families: [ 'FontAwesome' ],
      urls: [ '//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css']
    }
  };
  (function() {
    var wf = document.createElement('script');
    wf.src = ('https:' == document.location.protocol ? 'https' : 'http') +
        '://ajax.googleapis.com/ajax/libs/webfont/1/webfont.js';
    wf.type = 'text/javascript';
    wf.async = 'true';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(wf, s);
  })();
</script>

</body>
</html>
