sections:
  - title: Download and set-up
    level: 3
    items:
      - type: sub-section
        data:
          title: Bower
          level: 4
          items:
            - type: text
              data:
                text: 'The easiest way to get started with Chartist.js is by using <a href="http://bower.io/" target="_blank">bower</a>:'

            - type: code
              data:
                lang: bash
                code: bower install chartist --save

            - type: text
              data:
                text: >
                        The bower package contains the JavaScript library, CSS as well as the Sass (SCSS) files.
                        You can then integrate the desired dependencies in your project and start using them
                        immediately.

      - type: sub-section
        data:
          title: One, two, three, CSS!
          level: 4
          items:
            - type: text
              data:
                text: >
                        The quickest way to get things up and running is by using the Chartist.js CSS files.
                        The CSS is compiled from the Sass files with the default class names which are also
                        configured in the JavaScript library. You can then override the default styles or
                        modify the CSS file, however, for customization it's recommended to use the Sass
                        version of Chartist.js.

            - type: code-snippet
              data:
                id: simple-start
                lang: html

            - type: heading
              data:
                level: 5
                title: Use a CDN alternatively

            - type: text
              data:
                text: >
                        If you'd like to get started even faster you can also use a CDN to load Chartist.js. The
                        awesome people at jsDelivr provide a fantastic job in hosting libraries from over 42 POP Locations
                        around the world! They always update Chartist.js to the latest version immediately and
                        they do all that for free! Check out the <a href="http://www.jsdelivr.com/" target="_blank">jsDeliver website</a>
                        for more information.

            - type: code-snippet
              data:
                id: simple-start-cdn
                lang: html
                button: Show CDN Code

      - type: sub-section
        data:
          title: The Sass way
          level: 4
          items:
            - type: text
              data:
                text: >
                        If you like to customize your charts you can either remove the CSS fully and write your
                        own selectors using the Chartist.js Sass mixins or you just use the Chartist.js Sass
                        settings file to customize the look and feel of your charts.

            - type: text
              data:
                text: >
                        Styling inline SVG with CSS is a breeze and you should also consider writing your own
                        selectors for your charts and using the Sass mixins. You can read more about using the
                        Sass mixins in the <a href="#advanced">advanced</a> section.

            - type: text
              data:
                text: >
                        To customize the style of your charts using the Sass settings file you should copy the
                        settings file to your own Sass folder.

            - type: code
              data:
                lang: bash
                code: cp bower_components/chartist/dist/scss/settings/_chartist-settings.scss styles

            - type: text
              data:
                text: >
                        Then just import your copy of the settings file before you import the chartist.scss
                        file and change the settings in your copy as desired.

            - type: code-snippet
              data:
                id: custom-include
                lang: scss

            - type: heading
              data:
                level: 5
                title: Default settings

            - type: text
              data:
                text: >
                        The settings file contains all relevant variables used in the mixins and while
                        generating the default classes. You can simply change the settings for styling your
                        own charts. If you want to override certain settings based on state or pseeudo
                        selectors, you can use the individual mixins to only override specific styles.

            - type: text
              data:
                text: >
                        Take a look at the settings to see how to customize the style of the defalt Chartist.js
                        class selectors.

            - type: code-snippet
              data:
                id: default-sass-settings
                button: Show default settings
                path: src/styles/settings/_chartist-settings.scss
                lang: scss

  - title: Create your first chart
    level: 3
    items:
      - type: text
        data:
          text: >
                  In this section you'll go through a simple example of how to use Chartist.js in your project.
                  You'll learn the default stages you go through when creating and customizing a basic line chart.
                  If you'd like to see more in depth and advanced examples you should check out
                  the <a href="#advanced">advanced</a> section or the <a href="examples.html">examples page</a>.

      - type: sub-section
        data:
          title: As simple as it can get
          level: 4
          items:
            - type: text
              data:
                text: >
                        Chartist provides you a very simple API to get started, however, while trying to follow the
                         best practice of relying on standards and clear separation of concerns it sometimes needs a small mind
                         shift in order to understand how things are meant to work within Chartist. Instead of specifying
                         your colors, line width and other style related things in the JavaScript API, you'll need to use
                         CSS in order to control your appearance.

            - type: sub-section
              data:
                title: Creating a chart using aspect ratios
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              Because of the nature of responsive design it's important to understand that blocks in design like images,
                              videos and similar content need to be able to scale and adapt to the media. In order for an element to scale, you
                              need to rely on a certain aspect ratios (like 4:3, 3:2, 16:9 etc.) rather than specifying a fixed width and height.

                  - type: text
                    data:
                      text: >
                              To designers this Idea is absolutely not new, but to developers this might be at first. However, when a designer talks
                              to a developer about the images being 320x240 on this page and 300x200 on that element, he actually just
                              translated his idea of using 4:3 and 3:2 images into pixels.

                  - type: text
                    data:
                      text: >
                              With Chartist you can specify those ratios directly on containers without the need to calculate any fixed dimensions.
                              In order to create a chart that is using the aspect ratio of a golden section you can just add the class .ct-golden-section
                              to your container where you initialize Chartist.

                  - type: text
                    data:
                      text: >
                              Here is a list of all available container ratios (If using the Sass version of Chartist you can also easily add others):

                  - type: table
                    data:
                      id: container-aspect-ratio-classes
                      button: Show available aspect ratios
                      header:
                        - Container class
                        - Ratio
                      rows:
                        -
                          - .ct-square
                          - '1'
                        -
                          - .ct-minor-second
                          - '15:16'
                        -
                          - .ct-major-second
                          - '8:9'
                        -
                          - .ct-minor-third
                          - '5:6'
                        -
                          - .ct-major-third
                          - '4:5'
                        -
                          - .ct-perfect-fourth
                          - '3:4'
                        -
                          - .ct-perfect-fifth
                          - '2:3'
                        -
                          - .ct-minor-sixth
                          - '5:8'
                        -
                          - .ct-golden-section
                          - '1:1.618'
                        -
                          - .ct-major-sixth
                          - '3:5'
                        -
                          - .ct-minor-seventh
                          - '9:16'
                        -
                          - .ct-major-seventh
                          - '8:15'
                        -
                          - .ct-octave
                          - '1:2'
                        -
                          - .ct-major-tenth
                          - '2:5'
                        -
                          - .ct-major-eleventh
                          - '3:8'
                        -
                          - .ct-major-twelfth
                          - '1:3'
                        -
                          - .ct-double-octave
                          - '1:4'

                  - type: text
                    data:
                      text: >
                              Use the following HTML code to specify a container with one of the above aspect ratio classes.

                  - type: code
                    data:
                      code: '<div class="ct-chart ct-perfect-fourth"></div>'
                      lang: html

                  - type: text
                    data:
                      text: >
                              When using a fixed aspect ratio container you can then simply initialize your chart without
                              specifying any width or height in the options.

                  - type: code-snippet
                    data:
                      id: simple-start-aspect-ratio-chart
                      lang: js

            - type: sub-section
              data:
                title: Creating a chart with fixed dimensions
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              In order to create a simple line chart with fixed width and height you only need to have a
                              container element and initialize Chartist.js on it.
                              Give the container the class ct-chart so that it will get the default styles (if you don't
                              use your own classes).

                  - type: code-snippet
                    data:
                      id: simple-start-fixed-chart
                      lang: js

            - type: sub-section
              data:
                title: More than one chart on my page?
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              The example above uses the chartist default class to select the element where the chart should
                              be created. This class is important for applying the right styles in the CSS of Chartist. If
                              you need to create individual charts on one page, you should use IDs to initialize them separately.

                  - type: code-snippet
                    data:
                      id: two-charts
                      lang: html

      - type: sub-section
        data:
          title: The configuration of your chart
          level: 4
          items:
            - type: text
              data:
                text: >
                        Chartist.js is built very flexible and almost everything within your charts can be configured.
                        In the default settings (that you can check in the <a href="api-documentation.html">API Documentation</a>)
                        you'll get some predefined defaults applied to your charts.

            - type: text
              data:
                text: >
                        You can always override the default settings of your charts by passing in a configuration
                        object at creation time.

            - type: example-chart
              data:
                id: simple-configuration-chart
                classes: ct-golden-section
                show-code-button: Show code and comments

      - type: sub-section
        data:
          title: Responsive sugar topping
          level: 4
          items:
            - type: text
              data:
                text: >
                        Responsive web design is all based on
                        <a href="https://developer.mozilla.org/en-US/docs/Web/Guide/CSS/Media_queries" taget="_blank">media queries</a>
                        as you are probably already aware. Media queries are great and they help you to define media related
                        conditions that you can use to apply different styles for different media.
            - type: text
              data:
                text: >
                        Sometimes it's also required to have different behavior on certain media and it's possible that
                        a specific component of your web site should behave in an other way on a small media than on a
                        large one. Luckily there is window.matchMedia in your browser that comes to the rescue. With
                        matchMedia it's possible to let your javascript react differently based on CSS3 media queries.

            - type: heading
              data:
                level: 5
                title: Responsive setting overrides

            - type: text
              data:
                text: >
                        Configuring different chart behavior for various media is made simple with an override
                        mechanism. The priority of the override mechanism is based on order of specification of
                        the matching media queries.

            - type: text
              data:
                text: >
                        The following example uses different label interpolations (to save some space) on small media
                        as well as different spacing between the bars of the bar chart series. Resize your browser window
                        to see the effect.

            - type: example-chart
              data:
                id: example-simple-bar
                classes: ct-golden-section
                type: Bar
                show-code-button: Show code and comments

            - type: hint
              data:
                title: Cross-browser support
                classes: hint-cross-browser
                text: >
                        For IE9 you need to use a matchMedia polyfill. You should take a look at
                        <a href="https://github.com/paulirish/matchMedia.js/">Paul Irish's matchMedia polyfill</a>. Make
                        sure you include matchMedia.js as well as matchMedia.addListener.js as always both are needed
                        to polyfill the full specification of window.matchMedia.

      - type: sub-section
        data:
          title: Different configuration for different series
          level: 4
          items:
            - type: text
              data:
                text: >
                        Chartist also allows you to add specific configuration overrides for your series. This is useful
                        if you want to combine different looks for your series on the same chart.
            - type: text
              data:
                text: >
                        You can even take this one step further by again overriding your series configuration in some
                        responsive settings overrides. This sounds complicated but it actually is very simple. Check the
                        example code with inline comments below to see how easy it is.
            - type: example-chart
              data:
                id: example-line-series-override
                classes: ct-golden-section
                show-code-button: Show code

  - title: Styling your chart
    level: 3
    items:
      - type: text
        data:
          text: >
                  You should always use the power of CSS to make your data pop into your visitors eyes. I know
                  I’m telling this over and over but it’s all about a clean separation of concerns. In this chapter
                  you’ll find some useful information on how to style your charts with CSS.
      - type: sub-section
        data:
          title: Customizing the default CSS
          level: 4
          items:
            - type: text
              data:
                text: >
                        Chartist comes with a highly customizable Sass file that allows you to customize all sorts of
                        appearance relevant things on your charts including colours used for series, line styles,
                        thickness and other properties. If you have the advantage of having a Sass build process at
                        your disposal, I highly recommend you to use the Sass version of Chartist instead of the
                        already prebuilt CSS. For more information on how to use the Sass version
                        please <a href="#the-sass-way">check out the Sass way of Chartist</a>.

            - type: text
              data:
                text: >
                        Chartist generates predefined classes for series by default. Those class names are alpha
                        numerated and always start with <code>ct-series-a</code>, where the letter a will be iterated
                        with each series count (a, b, c, d etc.). To address a specific series in styling, you’ll
                        need to create some styles for the corresponding series class name.

            - type: text
              data:
                text: >
                        Here is a list of selectors from the different chart types with some comments to explain what
                        properties can be used to influence the visual style of your charts.

            - type: sub-section
              data:
                title: Line chart
                level: 5
                items:
                  - type: code-snippet
                    data:
                      id: custom-style-line-chart
                      lang: css

            - type: sub-section
              data:
                title: Bar chart
                level: 5
                items:
                  - type: code-snippet
                    data:
                      id: custom-style-bar-chart
                      lang: css

            - type: sub-section
              data:
                title: Pie chart
                level: 5
                items:
                  - type: code-snippet
                    data:
                      id: custom-style-pie-chart
                      lang: css

            - type: sub-section
              data:
                title: Donut chart
                level: 5
                items:
                  - type: code-snippet
                    data:
                      id: custom-style-donut-chart
                      lang: css

      - type: sub-section
        data:
          title: Colour up your charts
          level: 4
          items:
            - type: text
              data:
                text: >
                        Colour is a core attribute of appearance and as you already know, we believe in strong
                        separation of concerns in web development. Therefore, Chartist does not include any options
                        within the JavaScript API to control colour of your charts.
            - type: text
              data:
                text: >
                        This might first seem very inconvenient, but you’ll agree that it’s the right choice once you
                        need to change some colours in your web project where you’d need to go through some nasty
                        find-and-replace adventures in your JavaScript files to fix some colour mismatches.
            - type: text
              data:
                text: >
                        The prebuilt CSS version of Chartist ships with 15 default colours that will be applied to your
                        chart series elements. If you just want to override some of these colours I recommend you use
                        the information
                        found in <a href="#customizing-the-default-css">how to customize the default CSS of Chartist</a>.
            - type: text
              data:
                text: >
                        The following example illustrates how you’d override the colours of your line chart line and
                        point elements, within the two first sieries (ct-series-a and ct-series-b).
            - type: code-snippet
              data:
                id: colour-override-line-chart
                lang: css

            - type: text
              data:
                text: >
                        Note that on SVG elements there are two CSS properties for colourization. For strokes you
                        should use the <code>stroke</code> property and assign a CSS colour value. For fill areas you
                        can use the <code>fill</code> property. More information can be found in the above topics.

            - type: sub-section
              data:
                title: Dynamic colouring
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              Often, predefined classes which can be used to colour your charts are fine but sometimes
                              CSS is not flexible enough to control all facets of dynamic appearance. While we could
                              build semantic classes like level1, level2, level3 and so on, we could then assign a
                              class dynamically, but still we’d need to rely on discrete values defined in CSS. One
                              lack of CSS today is that it does not incorporate dynamic values like mouse position
                              or placeholder variables that can be linked to JavaScript.
                  - type: text
                    data:
                      text: >
                              So, how do you go about your heat map chart or dynamically calculated line chart
                              strokes based on your data?
                  - type: text
                    data:
                      text: >
                              Chartist offers you a powerful tool for such kind of chart manipulations. The intrusive
                              event pipeline of Chartist allows you to hook into a <code>draw</code> event that allows
                              you to modify all your charts elements on the go while they’re drawn.
                  - type: text
                    data:
                      text: >
                              The following live code example shows you how to make a dynamically coloured chart
                              based on some data. Please go ahead and play around with the example, that’s what live
                              coding is for! For more information on how to use the <code>Chartist.Svg</code> API,
                              please <a href="http://gionkunz.github.io/chartist-js/api-documentation.html#module-chartistsvg">check
                              the API documentation</a>.
            - type: live-example
              data:
                title: Using draw events to modify chart elements
                level: 5
                id: example-dynamic-colours
                classes: ct-golden-section
                intro: >
                         By using the intrusive event pipeline of Chartist, you can easily change the behaviour of your
                         charts in a consistent and convenient way.

  - title: Advanced
    level: 3
    items:
      - type: text
        data:
          text: >
                  In the following chapter you'll find some advanced usage examples that might be of interest for you.
                  Chartist is very flexible because it relies on standard technology. This also means that you will need
                  to implement certain things yourself. This topic should cover some of these use-cases and give you some
                  basic idea why and how to implement certain functionality.

      - type: sub-section
        data:
          title: Animations using Chartist.Svg
          level: 4
          items:
            - type: text
              data:
                text: >
                        Usually we recommend using CSS for animations as it's closer to a clean separation of concerns.
                        However, sometimes you would want to animate SVG properties that are not available in CSS to animate.
                        For this purpose we have added a simple but powerful animation API that allows you to create SMIL
                        animations in a more convenient way.
            - type: text
              data:
                text: >
                        In combination with the draw events of Chartist the animations are a very powerful and
                        flexible tool. You can intercept almost any step in chartist and if there is an SVG element
                        involved you can animate it using
                        <a href="api-documentation.html#chartistsvg-function-animate">Chartist.Svg.animate</a>.
            - type: text
              data:
                text: >
                        The following simple example shows you how to created a delayed fade in effect for the a scatter chart.
                        You can also edit the example to play around with the settings.

            - type: live-example
              data:
                title: Some SVG Animations can only be done with SMIL
                level: 5
                id: example-simple-svg-animation
                classes: ct-golden-section
                intro: >
                         Edit this example to figure out how to tweak animations. The force is strong in you young padawan!

      - type: sub-section
        data:
          title: Switching axis type
          level: 4
          items:
            - type: text
              data:
                text: >
                        The default configuration of the line chart is that it will use a step based X-Axis and an
                        automatic linear scale for the Y-Axis. This is all happening under the hood of Chartist and
                        already satisfies a lot of use-cases.
            - type: text
              data:
                text: >
                        Sometimes though, it's not enough to have a fixed step chart and you'd need something a bit
                        more complex. Something that allows you to plot those values along the X- and Y-Axis, instead
                        of just the Y-Axis. Sometimes, you'd also want to specify the ticks on your chart manually
                        rather than having them auto calculated from Chartist.
            - type: text
              data:
                text: >
                        Of course Chartist wouldn't let you down with all those ideas you have in mind on how to
                        visualize your data!

            - type: sub-section
              data:
                title: A line chart is only 1-dimensional?!
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              By default, Chartist uses <code>Chartist.StepAxis</code> for the X-Axis and <code>Chartist.AutoScaleAxis</code>
                              for the Y-Axis. Please read the <a href="api-documentation.html">API documentation</a>
                              if you'd like to get more details, or available options on axis configuration.
                  - type: text
                    data:
                      text: >
                              The step based axis will not use any value for projection but rather rely on the index
                              of the given value to determine the position. This is fine for discrete steps of one
                              dimensional data, like we know it from regular Chartist charts:
                  - type: code-snippet
                    data:
                      id: 1-dimension-values
                      lang: js
                  - type: text
                    data:
                      text: >
                              What really happens with such one dimensional series data under the hood is that it will
                              be transformed into two dimensional data like so:
                  - type: code-snippet
                    data:
                      id: 2-dimensions-values
                      lang: js
                  - type: text
                    data:
                      text: >
                              You can see that Chartist will automatically convert one dimensional data into two
                              dimensional values where the previous value is assigned to <code>y</code> and <code>x</code> will be set to
                              <code>undefined</code>. As the default axis for projecting the <code>x</code> value
                              is a <code>Chartist.StepAxis</code> that relies on the value index rather than the
                              value, this is perfectly fine for the default setup.


            - type: sub-section
              data:
                title: Creating an XY-plot chart
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              In order to plot on the X-Axis too, we'd need to switch the standard axis type
                              <code>Chartist.StepAxis</code> to something more continuous. Let's create a full fledged auto
                              scale chart that scales both the Y- and X-Axis automatically using
                              the <code>Chartist.AutoScaleAxis</code>!
                  - type: live-example
                    data:
                      title: Configure your axis type
                      level: 6
                      id: example-axis-auto
                      classes: ct-golden-section
                      intro: >
                               Note that we have just specified the X-Axis because the Y-Axis is configured to
                               <code>Chartist.AutoScaleAxis</code> by default.
                  - type: text
                    data:
                      text: >
                              A few things are different in this chart. Because
                              the <code>labels</code> array of our data is just used to be mapped as <code>ticks</code> option for the
                              default <code>Chartist.StepAxis</code> X-Axis, we can completely remove the labels from our data,
                              once we use a different axis than the step based one. Also you can see now from the
                              data series is that we are specifying 2-dimensional values directly.

            - type: sub-section
              data:
                title: Fixed stuff can be sweet too!
                level: 5
                items:
                  - type: text
                    data:
                      text: >
                              The following example shows how to create an axis with some fixed ticks.
                              The <code>Chartist.FixedScaleAxis</code> can be used to create a more rudimentary
                              continuous linear scale, where ticks can be fixed either with an array of values or
                              with a divisor. For the detailed options please check
                              the <a href="api-documentation.html">API documentation</a>.
                  - type: live-example
                    data:
                      title: Unleash your creativity...
                      level: 6
                      id: example-axis-fixed-and-auto
                      classes: ct-golden-section
                      intro: >
                               Let's say we'd like to fix a few ticks on the Y-Axis on our chart as those are the only
                               ones we're interested in. The X-Axis should be continuous too, but let's make
                               that axis a <code>Chartist.AutoScaleAxis</code>.
