{"name": "chartist", "title": "Chartist.js", "description": "Simple, responsive charts", "version": "0.9.7", "author": "<PERSON><PERSON>", "homepage": "https://gionkunz.github.io/chartist-js", "repository": {"type": "git", "url": "https://github.com/gionkunz/chartist-js.git"}, "bugs": {"url": "https://github.com/gionkunz/chartist-js/issues"}, "keywords": ["chartist", "responsive charts", "charts", "charting"], "files": ["dist", "LICENSE-WTFPL", "LICENSE-MIT", "package.json", "README.md"], "style": "dist/chartist.min.css", "main": "dist/chartist.js", "browser": "dist/chartist.js", "licenses": [{"type": "WTFPL", "url": "https://github.com/gionkunz/chartist-js/blob/master/LICENSE-WTFPL"}, {"type": "MIT", "url": "https://github.com/gionkunz/chartist-js/blob/master/LICENSE-MIT"}], "dependencies": {}, "devDependencies": {"assemble": "~0.4.42", "assemble-dox": "0.0.2", "grunt": "^0.4.5", "grunt-concurrent": "^1.0.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "~0.9.0", "grunt-contrib-copy": "^0.7.0", "grunt-contrib-cssmin": "^0.12.1", "grunt-contrib-htmlmin": "~0.4.0", "grunt-contrib-imagemin": "^1.0.0", "grunt-contrib-jasmine": "~0.8.2", "grunt-contrib-jshint": "~0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-watch": "^0.6.1", "grunt-critical": "0.1.1", "grunt-newer": "^1.1.0", "grunt-sass": "^1.1.0", "grunt-svgmin": "~2.0.0", "grunt-template": "^0.2.3", "grunt-umd": "~2.3.1", "grunt-usemin": "~3.0.0", "handlebars-helpers": "~0.5.8", "jasmine-jquery": "~2.0.6", "jshint-stylish": "~1.0.0", "load-grunt-config": "^0.16.0", "lodash": "~2.4.1", "seed-random": "~2.2.0", "time-grunt": "^1.0.0"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "config": {"banner": "/* Chartist.js <%= pkg.version %>\n * Copyright © <%= year %> Gion Kunz\n * Free to use under either the WTFPL license or the MIT license.\n * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-WTFPL\n * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-MIT\n */\n", "src": "src", "dist": "dist", "site": "site", "tmp": ".tmp", "public": ".public", "test": "test"}}