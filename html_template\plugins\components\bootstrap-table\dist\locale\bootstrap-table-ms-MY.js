/**
 * Bootstrap Table Malay translation
 * Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['ms-MY'] = {
        formatLoadingMessage: function () {
            return 'Permintaan sedang dimuatkan. Sila tunggu sebentar...';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' rekod setiap muka surat';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'Sedang memaparkan rekod ' + pageFrom + ' hingga ' + pageTo + ' daripada jumlah ' + totalRows + ' rekod';
        },
        formatSearch: function () {
            return 'Cari';
        },
        formatNoMatches: function () {
            return 'Tiada rekod yang menyamai permintaan';
        },
        formatPaginationSwitch: function () {
            return 'Tunjuk/sembunyi muka surat';
        },
        formatRefresh: function () {
            return 'Muatsemula';
        },
        formatToggle: function () {
            return 'Tukar';
        },
        formatColumns: function () {
            return 'Lajur';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['ms-MY']);

})(jQuery);
