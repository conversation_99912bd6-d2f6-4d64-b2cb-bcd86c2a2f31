gallery-examples:
  - id: example-gallery-one
    classes: ct-golden-section
  - id: example-gallery-two
    classes: ct-golden-section
  - id: example-gallery-three
    classes: ct-golden-section
  - id: example-gallery-four
    classes: ct-golden-section

sections:
  - title: Browser compatibility
    level: 4
    items:
      - type: browser-support-table
        data:
          browsers:
            - IE9
            - IE10
            - IE11
            - Firefox 31
            - Chrome 35
            - Safari 7
            - Safari 8
            - Android 4.3
            - Android 4.4
            - iOS Safari 6
            - iOS Safari 7
          features:
            - name: Overall Browser Support
              browsers:
                - name: IE9
                  status: supported
                  text: Supported
                - name: IE10
                  status: supported
                  text: Supported
                - name: IE11
                  status: supported
                  text: Supported
                - name: Firefox 31
                  status: supported
                  text: Supported
                - name: Chrome 35
                  status: supported
                  text: Supported
                - name: Safari 7
                  status: supported
                  text: Supported
                - name: Safari 8
                  status: supported
                  text: Supported
                - name: Android 4.3
                  status: supported
                  text: Supported
                - name: Android 4.4
                  status: supported
                  text: Supported
                - name: iOS Safari 6
                  status: supported
                  text: Supported
                - name: iOS Safari 7
                  status: supported
                  text: Supported
            - name: Multi-line Labels (foreignObject)
              browsers:
                - name: IE9
                  status: not-supported
                  text: Not supported
                - name: IE10
                  status: not-supported
                  text: Not supported
                - name: IE11
                  status: not-supported
                  text: Not supported
                - name: Firefox 31
                  status: supported
                  text: Supported
                - name: Chrome 35
                  status: supported
                  text: Supported
                - name: Safari 7
                  status: not-supported
                  text: Not supported
                - name: Safari 8
                  status: supported
                  text: Supported
                - name: Android 4.3
                  status: not-supported
                  text: Not supported
                - name: Android 4.4
                  status: supported
                  text: Supported
                - name: iOS Safari 6
                  status: not-supported
                  text: Not supported
                - name: iOS Safari 7
                  status: supported
                  text: Supported
            - name: Advanced CSS Animations
              browsers:
                - name: IE9
                  status: not-supported
                  text: Not supported
                - name: IE10
                  status: not-supported
                  text: Not supported
                - name: IE11
                  status: not-supported
                  text: Not supported
                - name: Firefox 31
                  status: supported
                  text: Supported
                - name: Chrome 35
                  status: supported
                  text: Supported
                - name: Safari 7
                  status: not-supported
                  text: Not supported
                - name: Safari 8
                  status: supported
                  text: Supported
                - name: Android 4.3
                  status: not-supported
                  text: Not supported
                - name: Android 4.4
                  status: supported
                  text: Supported
                - name: iOS Safari 6
                  status: supported
                  text: Supported
                - name: iOS Safari 7
                  status: supported
                  text: Supported
            - name: SVG Animations with SMIL
              browsers:
                - name: IE9
                  status: not-supported
                  text: Not supported
                - name: IE10
                  status: not-supported
                  text: Not supported
                - name: IE11
                  status: not-supported
                  text: Not supported
                - name: Firefox 31
                  status: supported
                  text: Supported
                - name: Chrome 35
                  status: supported
                  text: Supported
                - name: Safari 7
                  status: supported
                  text: Supported
                - name: Safari 8
                  status: supported
                  text: Supported
                - name: Android 4.3
                  status: supported
                  text: Supported
                - name: Android 4.4
                  status: supported
                  text: Supported
                - name: iOS Safari 6
                  status: supported
                  text: Supported
                - name: iOS Safari 7
                  status: supported
                  text: Supported
            - name: Responsive Options Override
              browsers:
                - name: IE9*
                  status: supported
                  text: Supported with polyfill
                - name: IE10
                  status: supported
                  text: Supported
                - name: IE11
                  status: supported
                  text: Supported
                - name: Firefox 31
                  status: supported
                  text: Supported
                - name: Chrome 35
                  status: supported
                  text: Supported
                - name: Safari 7
                  status: supported
                  text: Supported
                - name: Safari 8
                  status: supported
                  text: Supported
                - name: Android 4.3
                  status: supported
                  text: Supported
                - name: Android 4.4
                  status: supported
                  text: Supported
                - name: iOS Safari 6
                  status: supported
                  text: Supported
                - name: iOS Safari 7
                  status: supported
                  text: Supported

  - title: Projects / Wrapper libraries
    level: 4
    items:
      - type: text
        data:
          text: These projects and wrapper libraries are known to me right now that either use Chartist.js or wrap them into a library for usage in a framework. <strong>If you know other projects that use Chartist.js please let us know or make a <a href="https://github.com/gionkunz/chartist-js/blob/develop/site/data/pages/index.yml#L206" target="_blank">pull request for this file</a>.</strong>
      - type: table
        data:
          id: chartist-projects-table
          header:
            - Project
            - Type
          rows:
            -
              - '<a href="https://github.com/panosoft/node-chartist" target="_blank">node-chartist</a>'
              - Node Package for Server-side Charts
            -
              - '<a href="https://github.com/paradox41/ng-chartist.js" target="_blank">ng-chartist.js</a>'
              - Angular Directive
            -
              - '<a href="https://github.com/gruberb/chartistAngularDirective" target="_blank">chartistAngularDirective.js</a>'
              - Angular Directive
            -
              - '<a href="https://fraserxu.me/react-chartist" target="_blank">react-chartist</a>'
              - React Component
            -
              - '<a href="https://github.com/mfpierre/meteor-chartist-js" target="_blank">meteor-chartist-js</a>'
              - Meteor Package
            -
              - '<a href="https://github.com/soderlind/tablepress_chartist" target="_blank">tablepress_chartist</a>'
              - Wordpress / Tablepress Extension
            -
              - '<a href="https://github.com/tylergaw/ember-cli-chartist" target="_blank">ember-cli-chartist</a>'
              - Ember Addon
            -
              - '<a href="https://github.com/hatemalimam/ChartistJSF" target="_blank">ChartistJSF</a>'
              - Java Server Faces (Prime Faces) Component
  - title: Chart CSS animation example
    level: 3
    items:
      - type: example-chart
        data:
          id: example-line-months-interpolation
          classes: ct-golden-section dark
      - type: hint
        data:
          title: Cross-browser support
          classes: hint-cross-browser
          text: Note that CSS3 animations on SVG CSS attributes are not supported on all browsers and the appearance may vary.
    side-notes:
      - type: text
        data:
          text: Specifying the style of your chart in CSS is not only cleaner but also enables you to use awesome CSS animations and transitions to be applied to your SVG elements!

  - title: Crazy Animations with SMIL!
    level: 3
    items:
      - type: example-chart
        data:
          id: example-line-svg-animation
          classes: ct-golden-section
    side-notes:
      - type: text
        data:
          text: >
                  Almost limitless animation possibilities with the Chartist.Svg animation API. Checkout the
                  <a href="getting-started.html#animations-using-chartistsvg">advanced getting started guide about SVG
                  animations</a> with SMIL.

  - title: Responsive charts configuration
    level: 3
    items:
      - type: example-chart
        data:
          id: example-line-simple-responsive
          classes: ct-golden-section
          show-code-button: Show code
      - type: sub-section
        data:
          title: Configuration overrides based on media queries
          level: 4
          items:
            - type: text
              data:
                text: Configuring different chart behavior for various media is made simple with an override mechanism. The priority of the override mechanism is based on order of specification of the matching media queries.
            - type: hint
              data:
                title: Cross-browser support
                classes: hint-cross-browser
                text: For IE9 you need to use a matchMedia polyfill. You should take a look at <a href="https://github.com/paulirish/matchMedia.js/">Paul Irish's matchMedia polyfill</a>.
    side-notes:
      - type: text
        data:
          text: With the clear separation of concerns within Chartist.js you're able to style your charts with CSS in @media queries. However, sometimes it requires to also control the behaviour of your charts depending on the media. For this purpose Chartist.js provides you with a simple configuration override mechanism based on media queries.
