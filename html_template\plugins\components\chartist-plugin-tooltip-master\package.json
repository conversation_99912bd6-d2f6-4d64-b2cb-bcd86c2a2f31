{"name": "chartist-plugin-tooltips", "description": "Point Labels Plugin for Chartist.js", "version": "0.0.17", "author": "<PERSON>", "homepage": "https://github.com/Globegitter/chartist-plugin-tooltip", "repository": {"type": "git", "url": "git+https://github.com/Globegitter/chartist-plugin-tooltip.git"}, "bugs": {"url": "https://github.com/Globegitter/chartist-plugin-tooltip/issues"}, "keywords": ["chartist", "plugin", "tooltip"], "files": ["dist", "LICENSE", "package.json", "README.md"], "main": "dist/chartist-plugin-tooltip.js", "browser": "dist/chartist-plugin-tooltip.js", "licenses": [{"type": "MIT", "url": "https://github.com/Globegitter/chartist-plugin-tooltip/blob/master/LICENSE"}], "dependencies": {}, "devDependencies": {"chartist": "~0.7.3", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-copy": "^0.7.0", "grunt-contrib-jasmine": "~0.8.1", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-sass": "^0.9.2", "grunt-contrib-uglify": "^0.6.0", "grunt-newer": "^0.8.0", "grunt-umd": "~2.2.1", "jasmine-fixture": "~1.2.2", "jshint-stylish": "~1.0.0", "load-grunt-config": "^0.16.0", "time-grunt": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test", "preversion": "grunt test", "version": "grunt build && git add -A ."}, "config": {"banner": "/* chartist-plugin-tooltip <%= pkg.version %>\n * Copyright © <%= year %> <PERSON>\n * Free to use under the WTFPL license.\n * http://www.wtfpl.net/\n */\n", "src": "src", "dist": "dist", "tmp": ".tmp", "test": "test", "src_name": "chartist-plugin-tooltip"}, "directories": {"test": "test"}, "license": "MIT"}