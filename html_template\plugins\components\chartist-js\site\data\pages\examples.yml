sections:
  - title: Line chart examples
    level: 3
    items:
      - type: live-example
        data:
          title: Simple line chart
          level: 4
          id: simple-line-chart
          classes: ct-golden-section
          intro: An example of a simple line chart with three series. You can edit this example in realtime.
      - type: live-example
        data:
          title: Holes in data
          level: 4
          id: example-line-data-holes
          classes: ct-golden-section
          intro: >
                   Chart<PERSON> does not freak out if you have holes in your data. Instead it will render the lines in
                   segments and handles these holes gracefully. This also allows you to introduce a line at a later
                   point or to terminate the series before others.
      - type: live-example
        data:
          title: Filled holes in data
          level: 4
          id: example-line-data-fill-holes
          classes: ct-golden-section
          intro: >
                   Sometimes you don't want your data looking so fragmented, even when it really is.  That's why you
                   can also configure <PERSON><PERSON> to smooth over holes in your data.
      - type: live-example
        data:
          title: Only whole numbers
          level: 4
          id: example-line-only-integer
          classes: ct-golden-section
          intro: >
                   You're dealing with series that contain only whole numbers? It looks weird that <PERSON><PERSON> is creating
                   a scale that contains fractions even if your data does not? No problem! Tell the Chartist guy to
                   only use integers and he'll do the math!
      - type: live-example
        data:
          title: Line scatter diagram with responsive settings
          level: 4
          id: line-scatter-random
          classes: ct-golden-section
          intro: >
                   This advanced example uses a line chart to draw a scatter diagram. The data object is created
                   with a functional style random mechanism. There is a mobile first responsive configuration using
                   the responsive options to show less labels on small screens.
      - type: live-example
        data:
          title: Line chart with area
          level: 4
          id: example-line-area
          classes: ct-golden-section
          intro: >
                   This chart uses the showArea option to draw line, dots but also an area shape. Use the low option to
                   specify a fixed lower bound that will make the area expand. You can also use the areaBase property
                   to specify a data value that will be used to determine the area shape base position (this is 0 by default).
      - type: live-example
        data:
          title: Bi-polar Line chart with area only
          level: 4
          id: example-bipolar-line-area
          classes: ct-golden-section
          intro: >
                   You can also only draw the area shapes of the line chart. Area shapes will always be constructed around
                   their areaBase (that can be configured in the options) which also allows you to draw nice bi-polar
                   areas.
      - type: live-example
        data:
          title: Using events to replace graphics
          level: 4
          id: example-line-modify-drawing
          classes: ct-golden-section
          intro: >
                   Chartist has fixed graphical representations that are chosen because of their flexibility and to
                   provide a high level of separation of the concerns. However, sometimes you probably would like to use
                   different shapes or even images for your charts. One way to achieve this is by using the draw events and replace
                   or add custom SVG shapes.
      - type: live-example
        data:
          title: Advanced SMIL Animations
          level: 4
          id: example-line-svg-animation
          classes: ct-golden-section
          intro: >
                   Chartist provides a simple API to animate the elements on the Chart using SMIL. Usually you can achieve most
                   animation with CSS3 animations but in some cases you'd like to animate SVG properties that are not available
                   in CSS.
      - type: live-example
        data:
          title: SVG Path animation
          level: 4
          id: example-line-path-animation
          classes: ct-golden-section
          intro: >
                   Path animation is made easy with the <a href="api-documentation.html#module-chartistsvgpath">SVG Path API</a>.
                   The API allows you to modify complex SVG paths and transform them for different animation morphing states.
      - type: live-example
        data:
          title: Line Interpolation / Smoothing
          level: 4
          id: example-line-simple-smoothing
          classes: ct-golden-section
          intro: >
                   By default Chartist uses a cardinal spline algorithm to smooth the lines. However, like all other
                   things in Chartist, this can be customized easily! Check out
                   <a href="api-documentation.html#module-chartistinterpolation">the API Documentation</a> for more
                   smoothing options.
      - type: live-example
        data:
          title: Series Overrides
          level: 4
          id: example-line-series-override
          classes: ct-golden-section
          intro: >
                   By naming your series using the series object notation with a name property, you can enable the
                   individual configuration of series specific settings. <code>showLine</code>, <code>showPoint</code>,
                   <code>showArea</code> and even the smoothing function can be overriden per series! And guess what?
                   You can even override those series settings in the responsive configuration! Check the example code
                   for more details.
  - title: Bar chart examples
    level: 3
    items:
      - type: live-example
        data:
          title: Bi-polar bar chart
          level: 4
          id: bi-polar-bar-interpolated
          classes: ct-golden-section
          intro: >
                    A bi-polar bar chart with a range limit set with low and high. There is also an interpolation
                    function used to skip every odd grid line / label.
      - type: live-example
        data:
          title: Overlapping bars on mobile
          level: 4
          id: overlapping-bars
          classes: ct-golden-section
          intro: >
                    This example makes use of label interpolation and the seriesBarDistance property that allows you
                    to make bars overlap over each other. This then can be used to use the available space on mobile better.
                    Resize your browser to see the effect of the responsive configuration.
      - type: live-example
        data:
          title: Add peak circles using the draw events
          level: 4
          id: example-bar-with-circle-modify-drawing
          classes: ct-golden-section
          intro: >
                    With the help of draw events we are able to add a custom SVG shape to the peak of our bars.
      - type: live-example
        data:
          title: Multi-line labels
          level: 4
          id: example-multiline-bar
          classes: ct-golden-section
          intro: >
                    Chartist will figure out if your browser supports foreignObject and it will use them to create labels
                    that are based on regular HTML text elements. Multi-line and regular CSS styles are just two of many
                    benefits while using foreignObjects!
      - type: live-example
        data:
          title: Stacked bar chart
          level: 4
          id: stacked-bar
          classes: ct-golden-section
          intro: >
                    You can also set your bar chart to stack the series bars on top of each other easily by using the
                    <code>stackBars</code> property in your configuration.
      - type: live-example
        data:
          title: Horizontal bar chart
          level: 4
          id: example-bar-horizontal
          classes: ct-golden-section
          intro: >
                    Guess what! Creating horizontal bar charts is as simple as it can get. There's no new chart type
                    you need to learn, just passing an additional option is enough.
      - type: live-example
        data:
          title: Extreme responsive configuration
          level: 4
          id: example-bar-extreme-responsive
          classes: ct-golden-section
          intro: >
                    As all settings of a chart can be customized with the responsive configuration override mechanism of
                    Chartist, you can create a chart that adopts to every media condition!
      - type: live-example
        data:
          title: Distributed series
          level: 4
          id: example-bar-distributed-series
          classes: ct-golden-section
          intro: >
                    Sometime it's desired to have bar charts that show one bar per series distributed along the x-axis. If
                    this option is enabled, you need to make sure that you pass a single series array to Chartist that
                    contains the series values. In this example you can see T-shirt sales of a store categorized by size.
      - type: live-example
        data:
          title: Label placement
          level: 4
          id: example-bar-label-position
          classes: ct-golden-section
          intro: >
                    You can change the position of the labels on line and bar charts easily by using the <code>position</code>
                    option inside of the axis configuration.

  - title: Pie chart examples
    level: 3
    items:
      - type: live-example
        data:
          title: Simple pie chart
          level: 4
          id: simple-pie-chart
          classes: ct-golden-section ct-negative-labels
          intro: A very simple pie chart with label interpolation to show percentage instead of the actual data series value.
      - type: live-example
        data:
          title: Pie chart with custom labels
          level: 4
          id: pie-with-custom-labels
          classes: ct-golden-section
          intro: >
                    This pie chart is configured with custom labels specified in the data object. On desktop we use the labelOffset
                    property to offset the labels from the center. Also labelDirection can be used to control the direction
                    in which the labels are expanding.

      - type: live-example
        data:
          title: Gauge chart
          level: 4
          id: simple-gauge-chart
          classes: ct-golden-section ct-negative-labels
          intro: This pie chart uses donut, startAngle and total to draw a gauge chart.

      - type: live-example
        data:
          title: Animating a Donut with Svg.animate
          level: 4
          id: example-donut-animation
          classes: ct-golden-section
          intro: >
                    Although it'd be also possible to achieve this animation with CSS, with some minor suboptimal
                    things, here's an example of how to animate donut charts using Chartist.Svg.animate and SMIL.
