<x-guest-layout>
    <form class="form-horizontal form-material" id="registerform" method="POST" action="{{ route('register') }}">
        @csrf
        <h3 class="box-title m-b-20">Sign Up</h3>

        <!-- Name -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control @error('name') is-invalid @enderror"
                       type="text"
                       name="name"
                       value="{{ old('name') }}"
                       required
                       autofocus
                       autocomplete="name"
                       placeholder="Name">
                @error('name')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Email Address -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control @error('email') is-invalid @enderror"
                       type="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       autocomplete="username"
                       placeholder="Email">
                @error('email')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control @error('password') is-invalid @enderror"
                       type="password"
                       name="password"
                       required
                       autocomplete="new-password"
                       placeholder="Password">
                @error('password')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Confirm Password -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control @error('password_confirmation') is-invalid @enderror"
                       type="password"
                       name="password_confirmation"
                       required
                       autocomplete="new-password"
                       placeholder="Confirm Password">
                @error('password_confirmation')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Terms Agreement -->
        <div class="form-group mb-3">
            <div class="col-12">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="checkbox-signup" required>
                    <label class="form-check-label" for="checkbox-signup">
                        I agree to all <a href="#" class="text-primary">Terms</a>
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center mt-3 mb-3">
            <div class="col-12">
                <button class="btn btn-info btn-lg w-100 text-uppercase waves-effect waves-light" type="submit" id="register-btn">
                    Sign Up
                </button>
            </div>
        </div>

        <!-- Login Link -->
        <div class="form-group mb-0">
            <div class="col-12 text-center">
                <p>Already have an account? <a href="{{ route('login') }}" class="text-primary ms-1"><b>Sign In</b></a></p>
            </div>
        </div>
    </form>

    <script>
        $(document).ready(function() {
            $('#registerform').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true,
                        remote: "/api/check-email"
                    },
                    password: {
                        required: true,
                        minlength: 8
                    },
                    password_confirmation: {
                        required: true,
                        equalTo: "#registerform input[name='password']"
                    }
                },
                messages: {
                    name: {
                        required: "Name is required",
                        minlength: "Name must be at least 2 characters"
                    },
                    email: {
                        required: "Email is required",
                        email: "Please enter a valid email",
                        remote: "Email already exists"
                    },
                    password: {
                        required: "Password is required",
                        minlength: "Password must be at least 8 characters"
                    },
                    password_confirmation: {
                        required: "Please confirm password",
                        equalTo: "Passwords do not match"
                    }
                },
                errorClass: 'text-danger',
                highlight: function(element) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function(element) {
                    $(element).removeClass('is-invalid');
                },
                submitHandler: function(form) {
                    $('#register-btn').html('<i class="fas fa-spinner fa-spin"></i> Creating Account...').prop('disabled', true);
                    form.submit();
                }
            });
        });
    </script>
</x-guest-layout>
