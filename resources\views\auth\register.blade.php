<x-guest-layout>
    <form class="form-horizontal form-material" id="loginform" method="POST" action="{{ route('register') }}">
        @csrf
        <h3 class="box-title m-b-20">Sign Up</h3>

        <!-- Name -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('name') is-invalid @enderror"
                       type="text"
                       name="name"
                       value="{{ old('name') }}"
                       required
                       autofocus
                       autocomplete="name"
                       placeholder="Name">
                @error('name')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Email Address -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('email') is-invalid @enderror"
                       type="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       autocomplete="username"
                       placeholder="Email">
                @error('email')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Password -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('password') is-invalid @enderror"
                       type="password"
                       name="password"
                       required
                       autocomplete="new-password"
                       placeholder="Password">
                @error('password')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Confirm Password -->
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control @error('password_confirmation') is-invalid @enderror"
                       type="password"
                       name="password_confirmation"
                       required
                       autocomplete="new-password"
                       placeholder="Confirm Password">
                @error('password_confirmation')
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        <!-- Terms Agreement -->
        <div class="form-group">
            <div class="col-md-12">
                <div class="checkbox checkbox-primary p-t-0">
                    <input id="checkbox-signup" type="checkbox" required>
                    <label for="checkbox-signup"> I agree to all <a href="#" class="text-primary">Terms</a></label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center m-t-20">
            <div class="col-xs-12">
                <button class="btn btn-info btn-lg btn-block text-uppercase waves-effect waves-light" type="submit">
                    Sign Up
                </button>
            </div>
        </div>

        <!-- Login Link -->
        <div class="form-group m-b-0">
            <div class="col-sm-12 text-center">
                <p>Already have an account? <a href="{{ route('login') }}" class="text-primary m-l-5"><b>Sign In</b></a></p>
            </div>
        </div>
    </form>
</x-guest-layout>
