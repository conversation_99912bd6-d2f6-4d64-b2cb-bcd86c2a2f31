Social Buttons for Bootstrap
============================

Social Buttons made in pure CSS based on
[Bootstrap](http://twbs.github.io/bootstrap/) and
[Font Awesome](http://fortawesome.github.io/Font-Awesome/)!

[Check the live demo!](http://lipis.github.io/bootstrap-social)

Installation
------------

Include the `bootstrap-social.css` or `bootstrap-social.less` in your project, or
install it through [Bower](http://bower.io/):

    bower install bootstrap-social

Available classes
-----------------
 - `btn-adn`
 - `btn-bitbucket`
 - `btn-dropbox`
 - `btn-facebook`
 - `btn-flickr`
 - `btn-foursquare`
 - `btn-github`
 - `btn-google-plus`
 - `btn-instagram`
 - `btn-linkedin`
 - `btn-microsoft`
 - `btn-openid`
 - `btn-reddit`
 - `btn-soundcloud`
 - `btn-tumblr`
 - `btn-twitter`
 - `btn-vimeo`
 - `btn-vk`
 - `btn-yahoo`

Examples
--------

```html
<a class="btn btn-block btn-social btn-twitter">
  <i class="fa fa-twitter"></i>
  Sign in with Twitter
</a>

<a class="btn btn-social-icon btn-twitter">
  <i class="fa fa-twitter"></i>
</a>
```

Pull Requests
-------------
If you are about to create a new **Pull Request** for adding a new button don't
update the minified `bootstrap-social.css` file. It will be generated
automatically after a successful merge.
