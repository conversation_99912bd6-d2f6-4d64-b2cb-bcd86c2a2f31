{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "3.3.6", "keywords": ["css", "less", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "http://getbootstrap.com", "author": {"name": "Twitter, Inc."}, "scripts": {"test": "grunt test"}, "style": "dist/css/bootstrap.css", "less": "less/bootstrap.less", "main": "./dist/js/npm", "repository": {"type": "git", "url": "git+https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "license": "MIT", "devDependencies": {"btoa": "~1.1.2", "glob": "~6.0.1", "grunt": "~0.4.5", "grunt-autoprefixer": "~3.0.3", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-compress": "~0.14.0", "grunt-contrib-concat": "~0.5.1", "grunt-contrib-connect": "~0.11.2", "grunt-contrib-copy": "~0.8.0", "grunt-contrib-csslint": "~0.5.0", "grunt-contrib-cssmin": "~0.14.0", "grunt-contrib-htmlmin": "~0.6.0", "grunt-contrib-jade": "~0.15.0", "grunt-contrib-jshint": "~0.11.2", "grunt-contrib-less": "~1.1.0", "grunt-contrib-qunit": "~0.7.0", "grunt-contrib-uglify": "~0.9.2", "grunt-contrib-watch": "~0.6.1", "grunt-csscomb": "~3.1.0", "grunt-exec": "~0.4.6", "grunt-html": "~5.0.1", "grunt-jekyll": "~0.4.2", "grunt-jscs": "~2.3.0", "grunt-saucelabs": "~8.6.1", "grunt-sed": "github:twbs/grunt-sed#v0.2.0", "load-grunt-tasks": "~3.3.0", "markdown-it": "^5.0.0", "npm-shrinkwrap": "^200.4.0", "time-grunt": "^1.2.1"}, "engines": {"node": ">=0.10.1"}, "files": ["dist", "fonts", "grunt", "js/*.js", "less/**/*.less", "Gruntfile.js", "LICENSE"], "jspm": {"main": "js/bootstrap", "shim": {"js/bootstrap": {"deps": "j<PERSON>y", "exports": "$"}}, "files": ["css", "fonts", "js"]}, "gitHead": "81df608a40bf0629a1dc08e584849bb1e43e0b7a", "_id": "bootstrap@3.3.6", "_shasum": "8de8f749dc8a743f2ac5b510d988371e3daa6589", "_from": "bootstrap@latest", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "twbs", "email": "<EMAIL>"}, "dist": {"shasum": "8de8f749dc8a743f2ac5b510d988371e3daa6589", "tarball": "https://registry.npmjs.org/bootstrap/-/bootstrap-3.3.6.tgz"}, "maintainers": [{"name": "twbs", "email": "<EMAIL>"}], "directories": {}, "_resolved": "https://registry.npmjs.org/bootstrap/-/bootstrap-3.3.6.tgz", "readme": "ERROR: No README data found!"}