<?php $__env->startPush('css'); ?>
<style>
/* Login Form Styles */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Session Status -->
    <?php if(session('status')): ?>
        <div class="alert alert-success mb-3" role="alert">
            <?php echo e(session('status')); ?>

        </div>
    <?php endif; ?>

    <form class="form-horizontal form-material" id="loginform" method="POST" action="<?php echo e(route('login')); ?>">
        <?php echo csrf_field(); ?>
        <h3 class="box-title m-b-20">Sign In</h3>

        <!-- Email Address -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="email"
                       name="email"
                       value="<?php echo e(old('email')); ?>"
                       required
                       autofocus
                       autocomplete="username"
                       placeholder="Username">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <div class="col-12">
                <input class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       type="password"
                       name="password"
                       required
                       autocomplete="current-password"
                       placeholder="Password">
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger" style="font-size: 12px; margin-top: 5px;">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="form-group mb-3">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="remember" id="checkbox-signup" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="checkbox-signup">
                        Remember me
                    </label>
                </div>
                <?php if(Route::has('password.request')): ?>
                    <a href="javascript:void(0)" id="to-recover" class="text-dark">
                        <i class="fas fa-lock me-1"></i> Forgot pwd?
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center mt-3 mb-3">
            <div class="col-12">
                <button class="btn btn-info btn-lg w-100 text-uppercase waves-effect waves-light" type="submit">
                    Log In
                </button>
            </div>
        </div>

        <!-- Social Login -->
        <div class="row">
            <div class="col-12 mt-3 text-center">
                <div class="social">
                    <a href="javascript:void(0)" class="btn btn-facebook me-2" data-bs-toggle="tooltip" title="Login with Facebook">
                        <i aria-hidden="true" class="fab fa-facebook-f"></i>
                    </a>
                    <a href="javascript:void(0)" class="btn btn-googleplus" data-bs-toggle="tooltip" title="Login with Google">
                        <i aria-hidden="true" class="fab fa-google"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Register Link -->
        <div class="form-group mb-0">
            <div class="col-12 text-center">
                <p>Don't have an account? <a href="<?php echo e(route('register')); ?>" class="text-primary ms-1"><b>Sign Up</b></a></p>
            </div>
        </div>
    </form>

    <!-- Forgot Password Form -->
    <form class="form-horizontal" id="recoverform" method="POST" action="<?php echo e(route('password.email')); ?>" style="display: none;">
        <?php echo csrf_field(); ?>
        <div class="form-group">
            <div class="col-xs-12">
                <h3>Recover Password</h3>
                <p class="text-muted">Enter your Email and instructions will be sent to you!</p>
            </div>
        </div>
        <div class="form-group">
            <div class="col-xs-12">
                <input class="form-control" type="email" name="email" required placeholder="Email">
            </div>
        </div>
        <div class="form-group text-center m-t-20">
            <div class="col-xs-12">
                <button class="btn btn-primary btn-lg btn-block text-uppercase waves-effect waves-light" type="submit">
                    Reset
                </button>
            </div>
        </div>
        <div class="form-group m-b-0">
            <div class="col-sm-12 text-center">
                <p><a href="javascript:void(0)" id="to-login" class="text-primary m-l-5"><b>Back to Login</b></a></p>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    // Toggle between login and forgot password forms
    $('#to-recover').click(function() {
        $('#loginform').slideUp();
        $('#recoverform').fadeIn();
    });

    $('#to-login').click(function() {
        $('#recoverform').slideUp();
        $('#loginform').fadeIn();
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.guest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\xampp\htdocs\laravel_twelve_starter\resources\views/auth/login.blade.php ENDPATH**/ ?>