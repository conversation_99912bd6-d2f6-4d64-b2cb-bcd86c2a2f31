.landing-page-header {
  background-color: $color-yellow;
  text-align: center;

  > .introduction-title {
    text-transform: uppercase;
    font-size: rem-calc(nth($modular-scale, 8));
    margin: 0;
    display: inline-block;
    width: 100%;
    font-weight: 700;
    color: white;
    line-height: 0.5;
    margin-top: 2rem;

    @include text-shadow(1px 1px mix(black, $color-yellow, 15%), 2px 2px mix(black, $color-yellow, 15%), 3px 3px mix(black, $color-yellow, 15%));

    @media #{$medium-up} {
      font-size: rem-calc(nth($modular-scale, 10));
    }

    > small {
      font-size: rem-calc(nth($modular-scale, 4));
      display: inline-block;
      width: 100%;
      @include text-shadow(1px 1px mix(black, $color-yellow, 15%), 2px 2px mix(black, $color-yellow, 15%));
    }
  }

  .library-statement {
    text-transform: uppercase;
    font-size: rem-calc(nth($modular-scale, 5));
    margin: 0;
    display: inline-block;
    width: 100%;
    font-weight: 700;
    color: white;

    @include text-shadow(1px 1px mix(black, $color-yellow, 15%), 2px 2px mix(black, $color-yellow, 15%));

    padding-bottom: $column-gutter;

    .important {
      text-decoration: underline;
    }
  }

  > .limiter {
    position: relative;
    max-width: 480px;
    margin: 0 auto;

    > figure {
      margin: 0;
      @include ct-responsive-svg-container($ratio: (1/1));

      > svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  > .call-to-action {
    text-align: center;
    padding-bottom: $column-gutter;
    padding-left: $column-gutter / 2;
    padding-right: $column-gutter / 2;

    > .button {
      color: white;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 300 !important;

      &:before {
        padding-right: 0.5em;
      }

      &.download {
        background-color: $color-red;
        &:hover,
        &:focus { background-color: darken($color-red, 10%); color: rgba(255, 255, 255, 1);}
      }

      &.contribute {
        background-color: $color-grapefruit;
        &:hover,
        &:focus { background-color: darken($color-grapefruit, 10%); color: rgba(255, 255, 255, 1);}
      }

      @media #{$small-only} {
        width: 100%;
        font-size: 1em;
        font-weight: 700 !important;
      }

      @media #{$medium-up} {
        &.download {
          margin-right: $column-gutter / 2;
        }
      }
    }
  }
}

.brwsrsup {
  text-align: center;

  .brwsrsup__support--supported {
    color: $color-green;
    @include icon($fa-var-check-circle, $icon-location: after, $icon-size: 1.5rem);
  }

  .brwsrsup__visually-hidden {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
  }
}

@media screen and (max-width: 1100px) {
  .brwsrsup {
    display: block;
    text-align: left;

    thead {
      display: none;
    }

    .brwsrsup__feature {
      display: block;
      padding: 10px 0;
      margin: 20px 0 10px 0;
    }

    tbody {
      display: block;
    }

    tr {
      display: block;
      &:after {
        content: "";
        display: table;
        clear: both;
      }
    }

    td {
      display: block;
    }

    .brwsrsup__support {
      diplay: block;
      padding: 5px;
      width: auto;
      float: left;
      margin: 0 10px 10px 0;

      &:before {
        content: attr(data-browser)" ";
      }
    }

    .brwsrsup__support--supported {
      background: $color-green;
      color: white;

      &:before {
        content: attr(data-browser)" ";
      }

      @include icon($fa-var-check-circle, $icon-location: after);
    }

    .brwsrsup__support--not-supported {
      background: $color-red;
      color: lighten($color-white, 10%);

      &:before {
        content: attr(data-browser)" ";
      }
      @include icon($fa-var-exclamation-circle, $icon-location: after);
    }
  }
}
