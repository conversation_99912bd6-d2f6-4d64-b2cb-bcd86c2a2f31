# Support requests

Please direct support requests to the [DataTables forums](https://datatables.net/forums), ensuring that you provide a link to a test page that shows the problem and a full description of the issue. If you require urgent help, [priority support](https://datatables.net/support) is available.


# Contributing code

If you are thinking of contributing code to DataTables, first of all, thank you! All fixes, patches and enhancements to DataTables are very warmly welcomed. In order to keep thing manageable, there are a number of guidelines that should be followed in order to ensure that your modification is included in DataTables as quickly as possible:

1. Make contributions in the DataTables/DataTablesSrc repo. Changes to the built files in the built repo (DataTables/DataTables) will not be accepted since they would be overwritten by the next build!

2. Follow the style of the code in the existing files. They might not be to everyone's tastes, but consistency is key for a mature project like DataTables. DataTables doesn't have a coding standards document, but simple common sense of following the same style as in the existing files is ideal. For example use tabs not spaces (as you will see all source files use tabs).

3. Link to a test page showing the bug you are fixing or the feature you are adding. This allows to me to quickly identify what is being changed and why. Don't worry about being verbose in pull requests - its much better to know exactly what is changing and why!

4. DataTables is a large and complex project and it isn't always possible or suitable to pull in every suggested change. Please don't be offended if a pull request is not merged in, it will explained why not if this is the case. Also it isn't always possible to fully check and test pull requests as quickly as I would like due to other commitments. Again this is no reflection on your pull request, just the busy life that we all lead! If you have any questions about your potential contribution and its place in the DataTables project structure, please ask ahead of time in the [DataTables forums](//datatables.net/forums).

5. Pull requests will only be accepted if you acknowledge that your contribution is offered under and will be made available under the project's existing license (MIT). If your initial pull request doesn't explicitly acknowledge this I'll ask before it is pulled in.