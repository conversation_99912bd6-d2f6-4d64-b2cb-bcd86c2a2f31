<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" type="image/png" sizes="16x16" href="../plugins/images/favicon.png">
    <title>Cubic Admin Template</title>
    <!-- ===== Bootstrap CSS ===== -->
    <link href="bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- ===== Plugin CSS ===== -->
    <!-- ===== Animation CSS ===== -->
    <link href="css/animate.css" rel="stylesheet">
    <!-- ===== Custom CSS ===== -->
    <link href="css/style.css" rel="stylesheet">
    <!-- ===== Color CSS ===== -->
    <link href="css/colors/default.css" id="theme" rel="stylesheet">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body class="mini-sidebar">
    <!-- Preloader -->
    <div class="preloader">
        <div class="cssload-speeding-wheel"></div>
    </div>
    <div id="wrapper">
        <!-- ===== Top-Navigation ===== -->
        <nav class="navbar navbar-default navbar-static-top m-b-0">
            <div class="navbar-header">
                <a class="navbar-toggle font-20 hidden-sm hidden-md hidden-lg " href="javascript:void(0)" data-toggle="collapse" data-target=".navbar-collapse">
                    <i class="fa fa-bars"></i>
                </a>
                <div class="top-left-part">
                    <a class="logo" href="index.html">
                        <b>
                            <img src="../plugins/images/logo.png" alt="home" />
                        </b>
                        <span>
                            <img src="../plugins/images/logo-text.png" alt="homepage" class="dark-logo" />
                        </span>
                    </a>
                </div>
                <ul class="nav navbar-top-links navbar-left hidden-xs">
                    <li>
                        <a href="javascript:void(0)" class="sidebartoggler font-20 waves-effect waves-light"><i class="icon-arrow-left-circle"></i></a>
                    </li>
                    <li>
                        <form role="search" class="app-search hidden-xs">
                            <i class="icon-magnifier"></i>
                            <input type="text" placeholder="Search..." class="form-control">
                        </form>
                    </li>
                </ul>
                <ul class="nav navbar-top-links navbar-right pull-right">
                    <li class="dropdown">
                        <a class="dropdown-toggle waves-effect waves-light font-20" data-toggle="dropdown" href="javascript:void(0);">
                            <i class="icon-speech"></i>
                            <span class="badge badge-xs badge-danger">6</span>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <li>
                                <div class="drop-title">You have 4 new messages</div>
                            </li>
                            <li>
                                <div class="message-center">
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="../plugins/images/users/1.jpg" alt="user" class="img-circle">
                                            <span class="profile-status online pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Pavan kumar</h5>
                                            <span class="mail-desc">Just see the my admin!</span>
                                            <span class="time">9:30 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="../plugins/images/users/2.jpg" alt="user" class="img-circle">
                                            <span class="profile-status busy pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Sonu Nigam</h5>
                                            <span class="mail-desc">I've sung a song! See you at</span>
                                            <span class="time">9:10 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="../plugins/images/users/3.jpg" alt="user" class="img-circle"><span class="profile-status away pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Arijit Sinh</h5>
                                            <span class="mail-desc">I am a singer!</span>
                                            <span class="time">9:08 AM</span>
                                        </div>
                                    </a>
                                    <a href="javascript:void(0);">
                                        <div class="user-img">
                                            <img src="../plugins/images/users/4.jpg" alt="user" class="img-circle">
                                            <span class="profile-status offline pull-right"></span>
                                        </div>
                                        <div class="mail-contnet">
                                            <h5>Pavan kumar</h5>
                                            <span class="mail-desc">Just see the my admin!</span>
                                            <span class="time">9:02 AM</span>
                                        </div>
                                    </a>
                                </div>
                            </li>
                            <li>
                                <a class="text-center" href="javascript:void(0);">
                                    <strong>See all notifications</strong>
                                    <i class="fa fa-angle-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="dropdown">
                        <a class="dropdown-toggle waves-effect waves-light font-20" data-toggle="dropdown" href="javascript:void(0);">
                            <i class="icon-calender"></i>
                            <span class="badge badge-xs badge-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-tasks animated slideInUp">
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 1</strong>
                                            <span class="pull-right text-muted">40% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 40%">
                                                <span class="sr-only">40% Complete (success)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 2</strong>
                                            <span class="pull-right text-muted">20% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: 20%">
                                                <span class="sr-only">20% Complete</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 3</strong>
                                            <span class="pull-right text-muted">60% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%">
                                                <span class="sr-only">60% Complete (warning)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="javascript:void(0);">
                                    <div>
                                        <p>
                                            <strong>Task 4</strong>
                                            <span class="pull-right text-muted">80% Complete</span>
                                        </p>
                                        <div class="progress progress-striped active">
                                            <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%">
                                                <span class="sr-only">80% Complete (danger)</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a class="text-center" href="javascript:void(0);">
                                    <strong>See All Tasks</strong>
                                    <i class="fa fa-angle-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="right-side-toggle">
                        <a class="right-side-toggler waves-effect waves-light b-r-0 font-20" href="javascript:void(0)">
                            <i class="icon-settings"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        <!-- ===== Top-Navigation-End ===== -->
        <!-- ===== Left-Sidebar ===== -->
        <aside class="sidebar" role="navigation">
            <div class="scroll-sidebar">
                <div class="user-profile">
                    <div class="dropdown user-pro-body">
                        <div class="profile-image">
                            <img src="../plugins/images/users/hanna.jpg" alt="user-img" class="img-circle">
                            <a href="javascript:void(0);" class="dropdown-toggle u-dropdown text-blue" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                <span class="badge badge-danger">
                                    <i class="fa fa-angle-down"></i>
                                </span>
                            </a>
                            <ul class="dropdown-menu animated flipInY">
                                <li><a href="javascript:void(0);"><i class="fa fa-user"></i> Profile</a></li>
                                <li><a href="javascript:void(0);"><i class="fa fa-inbox"></i> Inbox</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="javascript:void(0);"><i class="fa fa-cog"></i> Account Settings</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href=""><i class="fa fa-power-off"></i> Logout</a></li>
                            </ul>
                        </div>
                        <p class="profile-text m-t-15 font-16"><a href="javascript:void(0);"> Hanna Gover</a></p>
                    </div>
                </div>
                <nav class="sidebar-nav">
                    <ul id="side-menu">
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-screen-desktop fa-fw"></i> <span class="hide-menu"> Dashboard <span class="label label-rounded label-info pull-right">3</span></span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="index.html">Modern Version</a> </li>
                                <li> <a href="index2.html">Clean Version</a> </li>
                                <li> <a href="index3.html">Analytical Version</a> </li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-basket fa-fw"></i> <span class="hide-menu"> eCommerce </span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="index4.html">Dashboard</a> </li>
                                <li> <a href="products.html">Products</a> </li>
                                <li> <a href="product-detail.html">Product Detail</a> </li>
                                <li> <a href="product-edit.html">Product Edit</a> </li>
                                <li> <a href="product-orders.html">Product Orders</a> </li>
                                <li> <a href="product-cart.html">Product Cart</a> </li>
                                <li> <a href="product-checkout.html">Product Checkout</a> </li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-envelope-letter fa-fw"></i> <span class="hide-menu"> Inbox <span class="label label-rounded label-primary pull-right">5</span></span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="inbox.html">Mail Box</a> </li>
                                <li> <a href="inbox-detail.html">Mail Details</a> </li>
                                <li> <a href="compose.html">Compose Mail</a> </li>
                                <li> <a href="contact.html">Contact</a> </li>
                                <li> <a href="contact-detail.html">Contact Detail</a> </li>
                            </ul>
                        </li>
                        <li>
                            <a class="active waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-equalizer fa-fw"></i> <span class="hide-menu"> UI Elements<span class="label label-rounded label-danger pull-right">18</span></span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li><a href="panels-wells.html">Panels and Wells</a></li>
                                <li><a href="panel-ui-block.html">Panels With BlockUI</a></li>
                                <li><a href="portlet-draggable.html">Draggable Portlet</a></li>
                                <li><a href="buttons.html">Buttons</a></li>
                                <li><a href="tabs.html">Tabs</a></li>
                                <li><a href="modals.html">Modals</a></li>
                                <li><a href="progressbars.html">Progress Bars</a></li>
                                <li><a href="notification.html">Notifications</a></li>
                                <li><a href="carousel.html">Carousel</a></li>
                                <li><a href="user-cards.html">User Cards</a></li>
                                <li><a href="timeline.html">Timeline</a></li>
                                <li><a href="timeline-horizontal.html">Horizontal Timeline</a></li>
                                <li><a href="range-slider.html">Range Slider</a></li>
                                <li><a href="ribbons.html">Ribbons</a></li>
                                <li><a href="steps.html">Steps</a></li>
                                <li><a href="session-idle-timeout.html">Session Idle Timeout</a></li>
                                <li><a href="session-timeout.html">Session Timeout</a></li>
                                <li><a href="bootstrap.html">Bootstrap UI</a></li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-notebook fa-fw"></i> <span class="hide-menu"> Forms </span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li><a href="form-basic.html">Basic Forms</a></li>
                                <li><a href="form-layout.html">Form Layout</a></li>
                                <li><a href="icheck-control.html">Icheck Control</a></li>
                                <li><a href="form-advanced.html">Form Addons</a></li>
                                <li><a href="form-upload.html">File Upload</a></li>
                                <li><a href="form-dropzone.html">File Dropzone</a></li>
                                <li><a href="form-pickers.html">Form-pickers</a></li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-grid fa-fw"></i> <span class="hide-menu"> Tables</span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li><a href="basic-table.html">Basic Tables</a></li>
                                <li><a href="table-layouts.html">Table Layouts</a></li>
                                <li><a href="data-table.html">Data Table</a></li>
                                <li><a href="bootstrap-tables.html">Bootstrap Tables</a></li>
                                <li><a href="responsive-tables.html">Responsive Tables</a></li>
                                <li><a href="editable-tables.html">Editable Tables</a></li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-pie-chart fa-fw"></i> <span class="hide-menu"> Charts</span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li><a href="morris-chart.html">Morris Chart</a></li>
                                <li><a href="peity-chart.html">Peity Charts</a></li>
                                <li><a href="knob-chart.html">Knob Charts</a></li>
                                <li><a href="sparkline-chart.html">Sparkline charts</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="widgets.html" aria-expanded="false"><i class="icon-settings fa-fw"></i> <span class="hide-menu"> Widgets </span></a>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-bulb fa-fw"></i> <span class="hide-menu"> Icons</span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="simple-line.html">Simple Line</a> </li>
                                <li> <a href="fontawesome.html">Fontawesome</a> </li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-location-pin fa-fw"></i> <span class="hide-menu"> Maps</span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="map-google.html">Google Map</a> </li>
                                <li> <a href="map-vector.html">Vector Map</a> </li>
                            </ul>
                        </li>
                        <li>
                            <a class="waves-effect" href="javascript:void(0);" aria-expanded="false"><i class="icon-docs fa-fw"></i> <span class="hide-menu"> Pages<span class="label label-rounded label-success pull-right">18</span></span></a>
                            <ul aria-expanded="false" class="collapse">
                                <li> <a href="javascript:void(0);">Error Pages</a>
                                    <ul aria-expanded="false" class="collapse">
                                        <li> <a href="400.html">400</a> </li>
                                        <li> <a href="403.html">403</a> </li>
                                        <li> <a href="404.html">404</a> </li>
                                        <li> <a href="500.html">404</a> </li>
                                        <li> <a href="503.html">404</a> </li>
                                    </ul>
                                </li>
                                <li><a href="starter-page.html">Starter Page</a></li>
                                <li><a href="blank.html">Blank Page</a></li>
                                <li><a href="search-result.html">Search Result</a></li>
                                <li><a href="custom-scroll.html">Custom Scrolls</a></li>
                                <li><a href="login.html">Login Page</a></li>
                                <li><a href="lock-screen.html">Lock Screen</a></li>
                                <li><a href="recoverpw.html">Recover Password</a></li>
                                <li><a href="animation.html">Animations</a></li>
                                <li><a href="profile.html">Profile</a></li>
                                <li><a href="invoice.html">Invoice</a></li>
                                <li><a href="gallery.html">Gallery</a></li>
                                <li><a href="pricing.html">Pricing</a></li>
                                <li><a href="register.html">Register</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="calendar.html" aria-expanded="false"><i class="icon-calender fa-fw"></i> <span class="hide-menu"> Calendar</span></a>
                        </li>
                    </ul>
                </nav>
                <div class="p-30">
                    <span class="hide-menu">
                        <a href="https://themeforest.net/cart/configure_before_adding/22321104?license=regular&ref=?ref=jthemes&size=source&support=bundle_6month" target="_blank" class="btn btn-success">Buy Cubic Admin</a>
                        <a href="javascript:void(0);" target="_blank" class="btn btn-default m-t-15">Check Documentation</a>
                    </span>
                </div>
            </div>
        </aside>
        <!-- ===== Left-Sidebar-End ===== -->
        <!-- Page Content -->
        <div class="page-wrapper">
            <div class="container-fluid">
                <!--row -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">General Buttons </h3>
                            <p class="text-muted m-b-30">Use a classes <code>btn btn-default</code>to quickly create a General btn.</p>
                            <div class="row">
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-default">Default</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-info">Info</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-primary">Primary</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-success">Success</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-danger">Danger</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-warning">Warning</button>
                                </div>
                            </div>
                            <h3 class="m-b-0 m-t-30 box-title">Disabled Buttons </h3>
                            <p class="text-muted m-b-30">Use a classes <code>btn btn-default disabled</code>to quickly create a Disabled btn.</p>
                            <div class="row">
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-default disabled">Default</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-info disabled">Info</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-primary disabled">Primary</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-success disabled">Success</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-danger disabled">Danger</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-warning disabled">Warning</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--row -->
                <!--row -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">Outline Buttons </h3>
                            <p class="text-muted m-b-30">Use a classes <code>btn btn-default btn-outline</code>to quickly create a Outline btn.</p>
                            <div class="row">
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-default">Default</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-info">Info</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-primary">Primary</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-success">Success</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-danger">Danger</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-warning">Warning</button>
                                </div>
                            </div>
                            <h3 class="m-b-0 m-t-30 box-title">Rounded outline Buttons </h3>
                            <p class="text-muted m-b-30">Use a classes <code>btn btn-default btn-rounded</code>to quickly create a Rounded btn.</p>
                            <div class="row">
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-default">Default</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-info">Info</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-primary">Primary</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-success">Success</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-danger">Danger</button>
                                </div>
                                <div class="col-lg-2 col-sm-4 col-xs-12">
                                    <button class="btn btn-block btn-outline btn-rounded btn-warning">Warning</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--row -->
                <div class="row">
                    <div class="col-lg-6 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">All Size button</h3>
                            <p class="text-muted m-b-30">Use a class <code>btn-lg</code> <code>btn-sm</code> <code>btn-xs</code></p>
                            <div class="button-box">
                                <button class="btn btn-outline btn-default btn-lg">Large button</button>
                                <button class="btn btn-outline btn-default">Default button</button>
                                <button class="btn btn-outline btn-default btn-sm">Small button</button>
                                <button class="btn btn-outline btn-default btn-xs">Mini button</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">Block button</h3>
                            <p class="text-muted m-b-30">Use a class <code>btn-block</code></p>
                            <button class="btn btn-outline btn-default btn-lg btn-block">Block button</button>
                        </div>
                    </div>
                </div>
                <!--row -->
                <!--row -->
                <div class="row">
                    <div class="col-lg-4 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">Button with icons</h3>
                            <p class="text-muted m-b-30">Just add icon in i tag and text in span thats it </p>
                            <div class="button-box">
                                <button class="btn btn-outline btn-default waves-effect waves-light"> <i class="fa fa-heart m-r-5"></i> <span>Love</span></button>
                                <button class="btn btn-info waves-effect waves-light"><span>Love</span> <i class="fa fa-heart m-l-5"></i></button>
                                <button class="btn btn-success waves-effect waves-light" type="button"><span class="btn-label"><i class="fa fa-check"></i></span>Success</button>
                                <button class="btn btn-primary btn-rounded waves-effect waves-light" type="button"><span class="btn-label"><i class="fa fa-envelope-o"></i></span>Mail</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">Button with Dropdown & Dropup</h3>
                            <p class="text-muted m-b-30">Just add icon in i tag and text in span thats it </p>
                            <div class="button-box">
                                <div class="btn-group m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-info dropdown-toggle waves-effect waves-light" type="button">Dropdown <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group dropup m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-danger dropdown-toggle waves-effect waves-light" type="button">Dropdown <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-default btn-outline dropdown-toggle waves-effect waves-light" type="button"> <i class="fa fa-heart m-r-5"></i> <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group dropup">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-warning dropdown-toggle waves-effect waves-light" type="button"><i class="fa fa-heart m-r-5"></i> <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="m-b-0 box-title">Upload Buttons</h3>
                            <p class="text-muted m-b-30">Just add icon in i tag and text in span thats it </p>
                            <div class="button-box">
                                <div class="fileupload btn btn-info waves-effect waves-light"><span><i class="ion-upload m-r-5"></i>Upload</span>
                                    <input type="file" class="upload"> </div>
                                <div class="fileupload btn btn-danger btn-rounded waves-effect waves-light"><span><i class="ion-upload m-r-5"></i>Upload</span>
                                    <input type="file" class="upload"> </div>
                                <div class="fileupload btn btn-default btn-outline btn-rounded waves-effect waves-light"><span><i class="ion-upload m-r-5"></i>Upload</span>
                                    <input type="file" class="upload"> </div>
                                <div class="fileupload btn btn-default btn-outline waves-effect waves-light"><span><i class="ion-upload m-r-5"></i>Upload</span>
                                    <input type="file" class="upload"> </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--row -->
                <div class="row">
                    <div class="col-lg-6 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="box-title">Normal Circle Buttons</h3>
                            <button type="button" class="btn btn-default btn-circle"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-primary btn-circle"><i class="fa fa-list"></i> </button>
                            <button type="button" class="btn btn-success btn-circle"><i class="fa fa-link"></i> </button>
                            <button type="button" class="btn btn-info btn-circle"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-warning btn-circle"><i class="fa fa-times"></i> </button>
                            <button type="button" class="btn btn-danger btn-circle"><i class="fa fa-heart"></i> </button>
                            <br>
                            <br>
                            <h3 class="box-title">Large Circle Buttons</h3>
                            <button type="button" class="btn btn-default btn-circle btn-lg"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-primary btn-circle btn-lg"><i class="fa fa-list"></i> </button>
                            <button type="button" class="btn btn-success btn-circle btn-lg"><i class="fa fa-link"></i> </button>
                            <button type="button" class="btn btn-info btn-circle btn-lg"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-warning btn-circle btn-lg"><i class="fa fa-times"></i> </button>
                            <button type="button" class="btn btn-danger btn-circle btn-lg"><i class="fa fa-heart"></i> </button>
                            <br>
                            <br>
                            <h3 class="box-title">Extra Large Circle Buttons</h3>
                            <button type="button" class="btn btn-default btn-circle btn-xl"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-primary btn-circle btn-xl"><i class="fa fa-list"></i> </button>
                            <button type="button" class="btn btn-success btn-circle btn-xl"><i class="fa fa-link"></i> </button>
                            <button type="button" class="btn btn-info btn-circle btn-xl"><i class="fa fa-check"></i> </button>
                            <button type="button" class="btn btn-warning btn-circle btn-xl"><i class="fa fa-times"></i> </button>
                            <button type="button" class="btn btn-danger btn-circle btn-xl"><i class="fa fa-heart"></i> </button>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="box-title">Social Buttons <code class="font-12">btn btn-facebook</code></h3>
                            <div class="button-box">
                                <div class="button-list">
                                    <button class="btn btn-facebook waves-effect waves-light" type="button"> <i class="fa fa-facebook"></i> </button>
                                    <button class="btn btn-twitter waves-effect waves-light" type="button"> <i class="fa fa-twitter"></i> </button>
                                    <button class="btn btn-googleplus waves-effect waves-light" type="button"> <i class="fa fa-google-plus"></i> </button>
                                    <button class="btn btn-linkedin waves-effect waves-light" type="button"> <i class="fa fa-linkedin"></i> </button>
                                    <button class="btn btn-instagram waves-effect waves-light" type="button"> <i class="fa fa-instagram"></i> </button>
                                    <button class="btn btn-pinterest waves-effect waves-light" type="button"> <i class="fa fa-pinterest"></i> </button>
                                    <button class="btn btn-dribbble waves-effect waves-light" type="button"> <i class="fa fa-dribbble"></i> </button>
                                    <button class="btn btn-youtube waves-effect waves-light" type="button"> <i class="fa fa-youtube"></i> </button>
                                    <h3 class="m-t-20">With Circle <code class="font-12">btn btn-facebook btn-circle</code></h3>
                                    <button class="btn btn-facebook waves-effect btn-circle waves-light" type="button"> <i class="fa fa-facebook"></i> </button>
                                    <button class="btn btn-twitter waves-effect btn-circle waves-light" type="button"> <i class="fa fa-twitter"></i> </button>
                                    <button class="btn btn-googleplus waves-effect btn-circle waves-light" type="button"> <i class="fa fa-google-plus"></i> </button>
                                    <button class="btn btn-linkedin waves-effect btn-circle waves-light" type="button"> <i class="fa fa-linkedin"></i> </button>
                                    <button class="btn btn-instagram waves-effect btn-circle waves-light" type="button"> <i class="fa fa-instagram"></i> </button>
                                    <button class="btn btn-pinterest waves-effect btn-circle waves-light" type="button"> <i class="fa fa-pinterest"></i> </button>
                                    <button class="btn btn-dribbble waves-effect btn-circle waves-light" type="button"> <i class="fa fa-dribbble"></i> </button>
                                    <button class="btn btn-youtube waves-effect btn-circle waves-light" type="button"> <i class="fa fa-youtube"></i> </button>
                                    <h3 class="m-t-20">With Rounded <code class="font-12">btn btn-facebook btn-rounded</code></h3>
                                    <button class="btn btn-facebook waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-facebook"></i> </button>
                                    <button class="btn btn-twitter waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-twitter"></i> </button>
                                    <button class="btn btn-googleplus waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-google-plus"></i> </button>
                                    <button class="btn btn-linkedin waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-linkedin"></i> </button>
                                    <button class="btn btn-instagram waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-instagram"></i> </button>
                                    <button class="btn btn-pinterest waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-pinterest"></i> </button>
                                    <button class="btn btn-dribbble waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-dribbble"></i> </button>
                                    <button class="btn btn-youtube waves-effect btn-rounded waves-light" type="button"> <i class="fa fa-youtube"></i> </button>
                                    <p class="m-b-20"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--row -->
                <!--row -->
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="white-box">
                            <h3 class="box-title">Stylish1 btn</h3>
                            <div class="button-box">
                                <p class="text-muted m-b-30">Give class to the button <code>fcbtn btn btn-outline btn-primary btn-1b</code></p>
                                <button class="fcbtn btn btn-primary btn-outline btn-1b">Button</button>
                                <button class="fcbtn btn btn-info btn-outline btn-1b">Button</button>
                                <button class="fcbtn btn btn-warning btn-outline btn-1b">Button</button>
                                <button class="fcbtn btn btn-danger btn-outline btn-1b">Button</button>
                                <button class="fcbtn btn btn-success btn-outline btn-1b">Button</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="white-box">
                            <h3 class="box-title">Stylish2 btn</h3>
                            <p class="text-muted m-b-30">Give class to the button <code>fcbtn btn btn-outline btn-primary btn-1c</code></p>
                            <div class="button-box">
                                <button class="fcbtn btn btn-primary btn-outline btn-1c">Button</button>
                                <button class="fcbtn btn btn-info btn-outline btn-1c">Button</button>
                                <button class="fcbtn btn btn-warning btn-outline btn-1c">Button</button>
                                <button class="fcbtn btn btn-danger btn-outline btn-1c">Button</button>
                                <button class="fcbtn btn btn-success btn-outline btn-1c">Button</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="white-box">
                            <h3 class="box-title">Stylish3 btn</h3>
                            <div class="button-box">
                                <p class="text-muted m-b-30">Give class to the button <code>fcbtn btn btn-outline btn-primary btn-1d</code></p>
                                <button class="fcbtn btn btn-primary btn-outline btn-1d">Button</button>
                                <button class="fcbtn btn btn-info btn-outline btn-1d">Button</button>
                                <button class="fcbtn btn btn-warning btn-outline btn-1d">Button</button>
                                <button class="fcbtn btn btn-danger btn-outline btn-1d">Button</button>
                                <button class="fcbtn btn btn-success btn-outline btn-1d">Button</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="white-box">
                            <h3 class="box-title">Stylish4 btn</h3>
                            <div class="button-box">
                                <p class="text-muted m-b-30">Give class to the button <code>fcbtn btn btn-outline btn-primary btn-1e</code></p>
                                <button class="fcbtn btn btn-primary btn-outline btn-1e">Button</button>
                                <button class="fcbtn btn btn-info btn-outline btn-1e">Button</button>
                                <button class="fcbtn btn btn-warning btn-outline btn-1e">Button</button>
                                <button class="fcbtn btn btn-danger btn-outline btn-1e">Button</button>
                                <button class="fcbtn btn btn-success btn-outline btn-1e">Button</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-12">
                        <div class="white-box">
                            <h3 class="box-title">Group Button</h3>
                            <div class="btn-group m-b-20">
                                <button type="button" class="btn btn-default btn-outline waves-effect">Left</button>
                                <button type="button" class="btn btn-default btn-outline waves-effect">Middle</button>
                                <button type="button" class="btn btn-default btn-outline waves-effect">Right</button>
                            </div>
                            <br/>
                            <div class="btn-group btn-group-vertical m-b-20">
                                <button class="btn btn-default btn-outline waves-effect waves-light">Left</button>
                                <button class="btn btn-default btn-outline waves-effect waves-light">Middle</button>
                                <button class="btn btn-default btn-outline waves-effect waves-light">Right</button>
                            </div>
                            <div class="btn-group btn-group-justified m-b-20"> <a class="btn btn-default btn-outline waves-effect waves-light" role="button">Left</a> <a class="btn btn-default btn-outline waves-effect waves-light" role="button">Middle</a> <a class="btn btn-default btn-outline waves-effect waves-light" role="button">Right</a></div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default btn-outline waves-effect">1</button>
                                <button type="button" class="btn btn-info waves-effect">2</button>
                                <button type="button" class="btn btn-default btn-outline waves-effect">3</button>
                                <button type="button" class="btn btn-default btn-outline waves-effect">4</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6">
                        <div class="white-box">
                            <div class="button-box">
                                <h3 class="box-title">Animated dropdown</h3>
                                <div class="btn-group m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-info dropdown-toggle waves-effect waves-light" type="button">Flipinx <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu animated flipInX">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group dropdown m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-danger dropdown-toggle waves-effect waves-light" type="button">Flipiny <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu animated flipInY">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group m-r-10">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-default btn-outline dropdown-toggle waves-effect waves-light" type="button"> LightSpeedIn <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu animated lightSpeedIn">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                                <div class="btn-group dropdown">
                                    <button aria-expanded="false" data-toggle="dropdown" class="btn btn-warning dropdown-toggle waves-effect waves-light" type="button">Slideinup <span class="caret"></span></button>
                                    <ul role="menu" class="dropdown-menu animated slideInUp">
                                        <li><a href="#">Action</a></li>
                                        <li><a href="#">Another action</a></li>
                                        <li><a href="#">Something else here</a></li>
                                        <li class="divider"></li>
                                        <li><a href="#">Separated link</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6">
                        <div class="white-box">
                            <h3 class="box-title">Stylish4 btn</h3>
                            <div class="button-box">
                                <p class="text-muted m-b-30">Give class to the button <code>fcbtn btn btn-outline btn-primary btn-1f</code></p>
                                <button class="fcbtn btn btn-primary btn-outline btn-1f">Button</button>
                                <button class="fcbtn btn btn-info btn-outline btn-1f">Button</button>
                                <button class="fcbtn btn btn-warning btn-outline btn-1f">Button</button>
                                <button class="fcbtn btn btn-danger btn-outline btn-1f">Button</button>
                                <button class="fcbtn btn btn-success btn-outline btn-1f">Button</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!--row -->
                <!-- ===== Right-Sidebar ===== -->
                <div class="right-sidebar">
                    <div class="slimscrollright">
                        <div class="rpanel-title"> Service Panel <span><i class="icon-close right-side-toggler"></i></span> </div>
                        <div class="r-panel-body">
                            <ul class="hidden-xs">
                                <li><b>Layout Options</b></li>
                                <li>
                                    <div class="checkbox checkbox-danger">
                                        <input id="headcheck" type="checkbox" class="fxhdr">
                                        <label for="headcheck"> Fix Header </label>
                                    </div>
                                </li>
                                <li>
                                    <div class="checkbox checkbox-warning">
                                        <input id="sidecheck" type="checkbox" class="fxsdr">
                                        <label for="sidecheck"> Fix Sidebar </label>
                                    </div>
                                </li>
                            </ul>
                            <ul id="themecolors" class="m-t-20">
                                <li><b>With Light sidebar</b></li>
                                <li><a href="javascript:void(0)" data-theme="default" class="default-theme working">1</a></li>
                                <li><a href="javascript:void(0)" data-theme="green" class="green-theme">2</a></li>
                                <li><a href="javascript:void(0)" data-theme="yellow" class="yellow-theme">3</a></li>
                                <li><a href="javascript:void(0)" data-theme="red" class="red-theme">4</a></li>
                                <li><a href="javascript:void(0)" data-theme="purple" class="purple-theme">5</a></li>
                                <li><a href="javascript:void(0)" data-theme="black" class="black-theme">6</a></li>
                                <li class="db"><b>With Dark sidebar</b></li>
                                <li><a href="javascript:void(0)" data-theme="default-dark" class="default-dark-theme">7</a></li>
                                <li><a href="javascript:void(0)" data-theme="green-dark" class="green-dark-theme">8</a></li>
                                <li><a href="javascript:void(0)" data-theme="yellow-dark" class="yellow-dark-theme">9</a></li>
                                <li><a href="javascript:void(0)" data-theme="red-dark" class="red-dark-theme">10</a></li>
                                <li><a href="javascript:void(0)" data-theme="purple-dark" class="purple-dark-theme">11</a></li>
                                <li><a href="javascript:void(0)" data-theme="black-dark" class="black-dark-theme">12</a></li>
                            </ul>
                            <ul class="m-t-20 chatonline">
                                <li><b>Chat option</b></li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/1.jpg" alt="user-img" class="img-circle"> <span>Varun Dhavan <small class="text-success">online</small></span></a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/2.jpg" alt="user-img" class="img-circle"> <span>Genelia Deshmukh <small class="text-warning">Away</small></span></a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/3.jpg" alt="user-img" class="img-circle"> <span>Ritesh Deshmukh <small class="text-danger">Busy</small></span></a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/4.jpg" alt="user-img" class="img-circle"> <span>Arijit Sinh <small class="text-muted">Offline</small></span></a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/5.jpg" alt="user-img" class="img-circle"> <span>Govinda Star <small class="text-success">online</small></span></a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)"><img src="../plugins/images/users/6.jpg" alt="user-img" class="img-circle"> <span>John Abraham<small class="text-success">online</small></span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- ===== Right-Sidebar-End ===== -->
            </div>
            <!-- /.container-fluid -->
            <footer class="footer t-a-c">
                <div class="bg-white p-20 ">            <center> 2017 © Cubic Admin / Design & Developed By <a href="https://jthemes.com" target="_blank">jThemes Studio</a> </center>        </div>
            </footer>
        </div>
        <!-- /#page-wrapper -->
    </div>
    <!-- /#wrapper -->
    <!-- jQuery -->
    <script src="../plugins/components/jquery/dist/jquery.min.js"></script>
    <!-- Bootstrap Core JavaScript -->
    <script src="bootstrap/dist/js/bootstrap.min.js"></script>
    <!-- Menu Plugin JavaScript -->
    <script src="js/sidebarmenu.js"></script>
    <!--slimscroll JavaScript -->
    <script src="js/jquery.slimscroll.js"></script>
    <!--Wave Effects -->
    <script src="js/waves.js"></script>
    <!-- Custom Theme JavaScript -->
    <script src="js/custom.js"></script>
    <!--Style Switcher -->
    <script src="../plugins/components/styleswitcher/jQuery.style.switcher.js"></script>
</body>

</html>
