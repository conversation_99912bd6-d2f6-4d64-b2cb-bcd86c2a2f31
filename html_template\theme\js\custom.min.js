$(function(){"use strict";$(".preloader").fadeOut();var i=function(){var i=window.innerWidth>0?window.innerWidth:this.screen.width,e=60;1170>i?($("body").addClass("mini-sidebar"),$(".top-left-part span").hide(),$(".scroll-sidebar, .slimScrollDiv").css("overflow-x","visible").parent().css("overflow","visible"),$(".sidebartoggler i").addClass("fa fa-bars")):($("body").removeClass("mini-sidebar"),$(".top-left-part span").show(),$(".sidebartoggler i").removeClass("fa fa-bars"));var s=(window.innerHeight>0?window.innerHeight:this.screen.height)-1;s-=e,1>s&&(s=1),s>e&&$(".page-wrapper").css("min-height",s+"px")};$(window).ready(i),$(window).on("resize",i),$(".sidebartoggler").on("click",function(){$("body").hasClass("mini-sidebar")?($("body").trigger("resize"),$(".scroll-sidebar, .slimScrollDiv").css("overflow","hidden").parent().css("overflow","visible"),$("body").removeClass("mini-sidebar"),$(".top-left-part span").show(),$(".sidebartoggler i").addClass("fa fa-bars")):($("body").trigger("resize"),$(".scroll-sidebar, .slimScrollDiv").css("overflow-x","visible").parent().css("overflow","visible"),$("body").addClass("mini-sidebar"),$(".top-left-part span").hide(),$(".sidebartoggler i").removeClass("fa fa-bars"))}),$(".navbar-toggle").on("click",function(){$("body").toggleClass("show-sidebar"),$(".navbar-toggle i").toggleClass("fa-bars"),$(".navbar-toggle i").addClass("fa-close")}),$(".sidebartoggler").on("click",function(){$(".sidebartoggler i").toggleClass("fa fa-bars")}),$(function(){for(var i=window.location,e=$("ul#side-menu a").filter(function(){return this.href==i}).addClass("active").parent().addClass("active");;){if(!e.is("li"))break;e=e.parent().addClass("in").parent().addClass("active")}}),$(".right-side-toggler").on("click",function(){$(".right-sidebar").slideDown(50),$(".right-sidebar").toggleClass("shw-rside"),$(".fxhdr").on("click",function(){$("body").toggleClass("fix-header")}),$(".fxsdr").on("click",function(){$("body").toggleClass("fix-sidebar")})}),$(function(){$("#side-menu").metisMenu()})}),function(i,e,s){var l='[data-perform="panel-collapse"]',o='[data-perform="panel-dismiss"]';i(l).each(function(){var e={toggle:!1},s=i(this).closest(".panel"),l=s.find(".panel-wrapper"),o=i(this).children("i");l.length||(l=s.children(".panel-heading").nextAll().wrapAll("<div/>").parent().addClass("panel-wrapper"),e={}),l.collapse(e).on("hide.bs.collapse",function(){o.removeClass("ti-minus").addClass("ti-plus")}).on("show.bs.collapse",function(){o.removeClass("ti-plus").addClass("ti-minus")})}),i(s).on("click",l,function(e){e.preventDefault();var s=i(this).closest(".panel"),l=s.find(".panel-wrapper");l.collapse("toggle")}),i(s).on("click",o,function(e){function l(){var e=s.parent();s.remove(),e.filter(function(){return i(this).is('[class*="col-"]')&&0===i(this).children("*").length}).remove()}e.preventDefault();var s=i(this).closest(".panel");l()})}(jQuery,window,document),$(function(){$('[data-toggle="tooltip"]').tooltip()}),$(function(){$('[data-toggle="popover"]').popover()}),$("#to-recover").on("click",function(){$("#loginform").slideUp(),$("#recoverform").fadeIn()}),$(".slimscrollright").slimScroll({height:"100%",position:"right",size:"5px",color:"#dcdcdc"}),$(".scroll-sidebar").slimScroll({position:"right",size:"5px",height:"100%",color:"#dcdcdc"}),$(".slimscrollsidebar").slimScroll({height:"100%",position:"right",size:"5px",color:"#dcdcdc"}),$(".chat-list").slimScroll({height:"100%",position:"right",size:"5px",color:"#dcdcdc"}),$(window).on("load",function(){$("body").trigger("resize")}),$("body").trigger("resize"),$(".visited li a").on("click",function(i){$(".visited li").removeClass("active");var e=$(this).parent();e.hasClass("active")||e.addClass("active"),i.preventDefault()});