<article class="module-member {{ctx.type}}">
  <header class="member-header">
    <h5 class="member-title" id="{{doxHash this}}"><span class="member-tag">{{ctx.type}}</span> {{ctx.string}}</h5>
    <button class="api-code-button" data-toggle-visible="#{{concat (doxHash this) '-code'}}">Show code</button>
  </header>
  <pre id="{{concat (doxHash this) '-code'}}">
    <code class="javascript">{{code}}</code>
  </pre>
  <div class="member-content">
    <div class="description">{{{description.summary}}}</div>

    {{#if (doxTagsOfType this 'param')}}
      <h6>Parameters</h6>
      {{#each (doxTagsOfType this 'param')}}
        <div class="param">
          <code>{{name}}</code>
          ({{#each types}}
            <span class="param-type"><code>{{this}}</code></span>
          {{/each}})
          <div class="param-description">{{description}}</div>
        </div>
      {{/each}}
    {{/if}}

    {{#if (doxTagsOfType this 'return')}}
      {{#with (doxTag this 'return')}}
        <h6>Returns</h6>

        <div class="return">
          ({{#each types}}
            <span class="param-type"><code>{{this}}</code></span>
          {{/each}})
          <div class="param-description">{{description}}</div>
        </div>
      {{/with}}
    {{/if}}

    {{#if (doxTagsOfType this 'example')}}
      <h6>Examples</h6>
      {{#each (doxTagsOfType this 'example')}}
        <pre class="api-example"><code class="js">{{string}}</code></pre>
      {{/each}}
    {{/if}}

  </div>

</article>