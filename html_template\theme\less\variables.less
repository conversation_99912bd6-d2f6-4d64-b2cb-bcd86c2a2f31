/* =============== Variables =============== */

@import url('https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i');
@basefont1: 'Open Sans',
sans-serif;

/* =============== Theme-Colors =============== */

@topbar1: #0078bc;
@topbar2: #00beda;
@sidebar: #ffffff;
@bodycolor: #f4f8f9;
@headingtext: #333b3f;
@themecolor: #0283cc;
@bodytext: #8d9498;
@sidebar-text: #8d9498;
@dark-text: #333b3f;
@linkcolor: #45a4da;

/* =============== Bootstrap-Colors =============== */

@danger: #e74a25;
@success: #2ecc71;
@warning: #ffb136;
@primary: #0283cc;
@info: #00bbd9;
@default: #dbe8eb;

/* =============== Other-Colors =============== */

@dark: #2b2b2b;
@inverse: #282f32;
@light: #e5ebec;
@extralight: #f7fafc;
@red: #d93c17;
@purple: #4a23ad;

/* =============== Normal-Color =============== */

@white: #ffffff;
@muted: #8d9498;
@blue: #0078bc;
@border: rgba(255,
255,
255,
0.2);
@common: #e5ebec;

/* =============== Other-Variables =============== */

@rgt: right;
@lft: left;
@radius: 0px;
.preloader {
    width: 100%;
    height: 100%;
    top: 0px;
    position: fixed;
    z-index: 99999;
    background: #fff;
    .cssload-speeding-wheel {
        position: absolute;
        top: ~"calc(50% - 3.5px)";
        left: ~"calc(50% - 3.5px)";
    }
}
