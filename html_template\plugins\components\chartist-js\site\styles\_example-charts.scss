#chartist-guy #chart-canvas {
  @include ct-chart($ct-text-color: darken($color-white, 50%), $ct-grid-color: darken($color-white, 20%));

  .ct-series-a {
    .ct-point {
      @include ct-chart-point($ct-point-shape: square, $ct-point-size: 8px);
    }

    .ct-line {
      @include ct-chart-line($ct-line-dasharray: 5px);
      @include animation(dashoffset, 1s linear infinite);
    }
  }
}

#example-line-months-interpolation {
  @include ct-chart($ct-text-color: lighten($color-gray, 30%), $ct-grid-color: lighten($color-gray, 5%), $ct-grid-dasharray: 3px);

  .ct-series-a {
    .ct-point {
      @include ct-chart-point($ct-point-shape: square);
    }

    .ct-line {
      @include ct-chart-line($ct-line-width: 4px, $ct-line-dasharray: 5px);
      @include animation(dashoffset, 1s linear infinite);
    }
  }

  .ct-series-b {
    .ct-point {
      @include animation(bouncing-stroke, 0.5s ease infinite);
    }

    .ct-line {
      @include ct-chart-line($ct-line-width: 3px);
    }
  }

  .ct-series-c {
    .ct-point {
      @include animation(exploding-stroke, 1s ease-out infinite);
    }

    .ct-line {
      @include ct-chart-line($ct-line-width: 2px, $ct-line-dasharray: 40px 3px);
    }
  }
}

#example-plugin-threshold {
  .ct-line {
    stroke-dasharray: 5px;
    animation: dashoffset 1s linear infinite;
  }

  .ct-line.ct-threshold-above, .ct-point.ct-threshold-above, .ct-bar.ct-threshold-above {
    stroke: #f05b4f;
  }

  .ct-line.ct-threshold-below, .ct-point.ct-threshold-below, .ct-bar.ct-threshold-below {
    stroke: #59922b;
  }

  .ct-area.ct-threshold-above {
    fill: #f05b4f;
  }

  .ct-area.ct-threshold-below {
    fill: #59922b;
  }
}

#example-plugin-fill-donut {
  .ct-chart-donut .ct-series-a .ct-slice-donut {
    stroke: #d70206;
  }

  .ct-chart-donut .ct-series-b .ct-slice-donut {
    stroke: rgba(0,0,0,.4);
    opacity: 0.0;
  }

  .ct-chart-donut .ct-fill-donut .ct-slice-donut {
    stroke: rgba(0,0,0,.4);
    opacity: 1;
  }

  .ct-fill-donut-label {
    h3 {
      font-weight: bolder;
    }

    small {
      font-size: 0.6em;
    }


    i {
      font-size: 1.5em;
    }

  }
}
#example-plugin-zoom {
  .ct-zoom-rect {
		  fill: rgba(200, 100, 100, 0.3);
		  stroke: red;
	  }
}
