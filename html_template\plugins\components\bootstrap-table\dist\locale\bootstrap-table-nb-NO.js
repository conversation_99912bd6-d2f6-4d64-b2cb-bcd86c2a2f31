/**
 * Bootstrap Table norwegian translation
 * Author: <PERSON>, <EMAIL>
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['nb-NO'] = {
        formatLoadingMessage: function () {
            return 'Oppdaterer, vennligst vent...';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' poster pr side';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'Viser ' + pageFrom + ' til ' + pageTo + ' av ' + totalRows + ' rekker';
        },
        formatSearch: function () {
            return 'Søk';
        },
        formatNoMatches: function () {
            return 'Ingen poster funnet';
        },
        formatRefresh: function () {
            return 'Oppdater';
        },
        formatToggle: function () {
            return 'Endre';
        },
        formatColumns: function () {
            return 'Kolonner';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['nb-NO']);

})(jQuery);