<!DOCTYPE html>  
<html lang="en">
<head>
  <title>End2end Test Runner</title>
</head>
<body>
<header class="page-header">
  <h1>Chartist.js
    <small> Simple responsive charts</small>
  </h1>
  <div class="limiter">
    <figure>
      <svg id="chartist-guy"></svg>
    </figure>
  </div>
</header>

<article class="main" role="main">
  <section class="documentation-section">
    <header>
      <h2>Chart CSS animation example</h2>
    </header>
    <div class="content">
      <div class="chart-container">
        <svg id="chart"></svg>
      </div>
    </div>
    <aside class="side-notes">
      <p>Specifying the style of your chart in CSS is not only cleaner but also enables you to use awesome CSS
        animations
        and transitions to be applied to your SVG elements!</p>
    </aside>
  </section>
</article>
</body>
</html>