<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Laravel')); ?></title>
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('template/images/favicon.png')); ?>">

    <!-- ===== Bootstrap 5.3.2 CSS ===== -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <!-- ===== Font Awesome 6.5.1 ===== -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">
    <!-- ===== Animate.css 4.1.1 ===== -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" integrity="sha512-c42qTSw/wPZ3/5LBzD+Bw5f7bSF2oxou6wEb+I/lqeaKV5FDIfMvvRp772y4jcJLKuGUOpbJMdg/BTl50fJYAw==" crossorigin="anonymous">
    <!-- ===== Custom CSS ===== -->
    <link href="<?php echo e(asset('template/css/style.css')); ?>" rel="stylesheet">
    <!-- ===== Color CSS ===== -->
    <link href="<?php echo e(asset('template/css/colors/default.css')); ?>" id="theme" rel="stylesheet">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <!-- Custom Validation Styles -->
    <style>
        /* jQuery Validation Error Styling */
        .invalid-feedback {
            display: block !important;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
            font-weight: 500;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-control.is-valid:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-control.is-invalid:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        /* Loading state for submit button */
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* Success message styling */
        .valid-feedback {
            display: block !important;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #28a745;
            font-weight: 500;
        }

        /* Password strength indicator */
        .password-strength {
            margin-top: 5px;
            font-size: 0.8rem;
        }

        .password-strength.weak {
            color: #dc3545;
        }

        .password-strength.medium {
            color: #ffc107;
        }

        .password-strength.strong {
            color: #28a745;
        }

        /* Email checking indicator */
        .email-checking {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        /* Form group spacing */
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        /* Validation icons */
        .form-control.is-valid {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            padding-right: calc(1.5em + 0.75rem);
        }

        .form-control.is-invalid {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            padding-right: calc(1.5em + 0.75rem);
        }
    </style>
</head>

<body class="mini-sidebar">
    <!-- Preloader -->
    <div class="preloader">
        <div class="cssload-speeding-wheel"></div>
    </div>

    <section id="wrapper" class="login-register">
        <div class="login-box">
            <div class="white-box">
                <?php echo e($slot); ?>

            </div>
        </div>
    </section>

    <!-- jQuery 3.7.1 -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- jQuery Validation Plugin 1.20.0 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.20.0/jquery.validate.min.js" integrity="sha512-WMEKGZ7L5LWgaPeJtw9MBM4i5w5OSBlSjTjCtSnvFJGSVD26gE5+Td12qN5pvWXhuWaWcVwF++F7aqu9cvqP0A==" crossorigin="anonymous"></script>
    <!-- jQuery Validation Additional Methods -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.20.0/additional-methods.min.js" integrity="sha512-TiQST7x/0aMjgVTcep29gi+q5Lk5gVTUPE9XgN0g96rwtjEjLpod4mlBRKWHeBcvGBAEvJBmfDqh2hfMMmg+5A==" crossorigin="anonymous"></script>
    <!-- Bootstrap 5.3.2 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <!-- Wave Effects (Updated) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/node-waves/0.7.6/waves.min.js" integrity="sha512-2Sv/ZWnW/dx5P9ZZXQiUlXU0PUzEVZbeIQIjX2ZQiLyVQHFoL2cZqRURSH/V8OLs1Hb7gzXnQGs6+Hj4LSbNiA==" crossorigin="anonymous"></script>
    <!-- Custom Theme JavaScript -->
    <script src="<?php echo e(asset('template/js/custom.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            // Hide preloader
            $('.preloader').fadeOut();

            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Waves effect
            if (typeof Waves !== 'undefined') {
                Waves.attach('.waves-effect', ['waves-light']);
                Waves.init();
            }

            // Toggle between login and recover password forms
            $('#to-recover').on('click', function(e) {
                e.preventDefault();
                $('#loginform').slideUp();
                $('#recoverform').fadeIn();
            });

            $('#to-login').on('click', function(e) {
                e.preventDefault();
                $('#recoverform').slideUp();
                $('#loginform').fadeIn();
            });

            // Initialize jQuery Validation for Register Form
            if ($('#registerform').length) {
                $('#registerform').validate({
                    rules: {
                        name: {
                            required: true,
                            minlength: 2,
                            maxlength: 50,
                            pattern: /^[a-zA-Z\s]+$/
                        },
                        email: {
                            required: true,
                            email: true,
                            maxlength: 100,
                            remote: {
                                url: "<?php echo e(route('api.check-email')); ?>",
                                type: "GET",
                                data: {
                                    email: function() {
                                        return $('#registerform input[name="email"]').val();
                                    }
                                },
                                dataFilter: function(data) {
                                    var json = JSON.parse(data);
                                    return json === true ? '"true"' : '"This email is already registered"';
                                }
                            }
                        },
                        password: {
                            required: true,
                            minlength: 8,
                            maxlength: 50,
                            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
                        },
                        password_confirmation: {
                            required: true,
                            equalTo: '#registerform input[name="password"]'
                        }
                    },
                    messages: {
                        name: {
                            required: "Please enter your full name",
                            minlength: "Name must be at least 2 characters long",
                            maxlength: "Name cannot exceed 50 characters",
                            pattern: "Name can only contain letters and spaces"
                        },
                        email: {
                            required: "Please enter your email address",
                            email: "Please enter a valid email address",
                            maxlength: "Email cannot exceed 100 characters"
                        },
                        password: {
                            required: "Please enter a password",
                            minlength: "Password must be at least 8 characters long",
                            maxlength: "Password cannot exceed 50 characters",
                            pattern: "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
                        },
                        password_confirmation: {
                            required: "Please confirm your password",
                            equalTo: "Passwords do not match"
                        }
                    },
                    errorElement: 'div',
                    errorClass: 'invalid-feedback',
                    validClass: 'is-valid',
                    errorPlacement: function(error, element) {
                        error.addClass('d-block');
                        element.closest('.form-group').append(error);
                    },
                    highlight: function(element) {
                        $(element).addClass('is-invalid').removeClass('is-valid');
                    },
                    unhighlight: function(element) {
                        $(element).addClass('is-valid').removeClass('is-invalid');
                    },
                    submitHandler: function(form) {
                        // Show loading state
                        var submitBtn = $(form).find('button[type="submit"]');
                        var originalText = submitBtn.html();
                        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Creating Account...').prop('disabled', true);

                        // Submit the form
                        form.submit();
                    }
                });

                // Add custom pattern validation method
                $.validator.addMethod("pattern", function(value, element, param) {
                    if (this.optional(element)) {
                        return true;
                    }
                    if (typeof param === "string") {
                        param = new RegExp("^(?:" + param + ")$");
                    }
                    return param.test(value);
                }, "Invalid format.");
            }
        });
    </script>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\laravel_twelve_starter\resources\views/layouts/guest.blade.php ENDPATH**/ ?>