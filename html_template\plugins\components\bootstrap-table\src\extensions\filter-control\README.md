# Table Filter Control

Use Plugin: [bootstrap-table-filter-control](https://github.com/wenzhixin/bootstrap-table/tree/master/src/extensions/filter-control)

## Usage

```html
<script src="extensions/filter-control/bootstrap-table-filter-control.js"></script>
```

## Options

### filterControl

* type: Boolean
* description: Set true to add an `input` or `select` into the column.
* default: `false`

## Column options

### filterControl

* type: String
* description: Set `input` or `select` to add one of those element into column.
* default: `undefined`

## Events

### onColumnSearch(column-search.bs.table)

* Fired when we are searching into the column data