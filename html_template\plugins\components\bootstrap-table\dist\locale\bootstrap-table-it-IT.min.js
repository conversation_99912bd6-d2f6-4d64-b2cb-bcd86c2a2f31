/*
* bootstrap-table - v1.8.1 - 2015-05-29
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2015 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";a.fn.bootstrapTable.locales["it-IT"]={formatLoadingMessage:function(){return"Caricamento in corso..."},formatRecordsPerPage:function(a){return a+" records per pagina"},formatShowingRows:function(a,b,c){return"Pagina "+a+" di "+b+" ("+c+" records)"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun record trovato"},formatRefresh:function(){return"Rinfrescare"},formatToggle:function(){return"Alternare"},formatColumns:function(){return"Colonne"}},a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales["it-IT"])}(jQuery);