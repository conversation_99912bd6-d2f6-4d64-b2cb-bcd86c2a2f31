$fa-var-glass: "\f000";
$fa-var-music: "\f001";
$fa-var-search: "\f002";
$fa-var-envelope-o: "\f003";
$fa-var-heart: "\f004";
$fa-var-star: "\f005";
$fa-var-star-o: "\f006";
$fa-var-user: "\f007";
$fa-var-film: "\f008";
$fa-var-th-large: "\f009";
$fa-var-th: "\f00a";
$fa-var-th-list: "\f00b";
$fa-var-check: "\f00c";
$fa-var-times: "\f00d";
$fa-var-search-plus: "\f00e";
$fa-var-search-minus: "\f010";
$fa-var-power-off: "\f011";
$fa-var-signal: "\f012";
$fa-var-cog: "\f013";
$fa-var-trash-o: "\f014";
$fa-var-home: "\f015";
$fa-var-file-o: "\f016";
$fa-var-clock-o: "\f017";
$fa-var-road: "\f018";
$fa-var-download: "\f019";
$fa-var-arrow-circle-o-down: "\f01a";
$fa-var-arrow-circle-o-up: "\f01b";
$fa-var-inbox: "\f01c";
$fa-var-play-circle-o: "\f01d";
$fa-var-repeat: "\f01e";
$fa-var-refresh: "\f021";
$fa-var-list-alt: "\f022";
$fa-var-lock: "\f023";
$fa-var-flag: "\f024";
$fa-var-headphones: "\f025";
$fa-var-volume-off: "\f026";
$fa-var-volume-down: "\f027";
$fa-var-volume-up: "\f028";
$fa-var-qrcode: "\f029";
$fa-var-barcode: "\f02a";
$fa-var-tag: "\f02b";
$fa-var-tags: "\f02c";
$fa-var-book: "\f02d";
$fa-var-bookmark: "\f02e";
$fa-var-print: "\f02f";
$fa-var-camera: "\f030";
$fa-var-font: "\f031";
$fa-var-bold: "\f032";
$fa-var-italic: "\f033";
$fa-var-text-height: "\f034";
$fa-var-text-width: "\f035";
$fa-var-align-left: "\f036";
$fa-var-align-center: "\f037";
$fa-var-align-right: "\f038";
$fa-var-align-justify: "\f039";
$fa-var-list: "\f03a";
$fa-var-outdent: "\f03b";
$fa-var-indent: "\f03c";
$fa-var-video-camera: "\f03d";
$fa-var-picture-o: "\f03e";
$fa-var-pencil: "\f040";
$fa-var-map-marker: "\f041";
$fa-var-adjust: "\f042";
$fa-var-tint: "\f043";
$fa-var-pencil-square-o: "\f044";
$fa-var-share-square-o: "\f045";
$fa-var-check-square-o: "\f046";
$fa-var-arrows: "\f047";
$fa-var-step-backward: "\f048";
$fa-var-fast-backward: "\f049";
$fa-var-backward: "\f04a";
$fa-var-play: "\f04b";
$fa-var-pause: "\f04c";
$fa-var-stop: "\f04d";
$fa-var-forward: "\f04e";
$fa-var-fast-forward: "\f050";
$fa-var-step-forward: "\f051";
$fa-var-eject: "\f052";
$fa-var-chevron-left: "\f053";
$fa-var-chevron-right: "\f054";
$fa-var-plus-circle: "\f055";
$fa-var-minus-circle: "\f056";
$fa-var-times-circle: "\f057";
$fa-var-check-circle: "\f058";
$fa-var-question-circle: "\f059";
$fa-var-info-circle: "\f05a";
$fa-var-crosshairs: "\f05b";
$fa-var-times-circle-o: "\f05c";
$fa-var-check-circle-o: "\f05d";
$fa-var-ban: "\f05e";
$fa-var-arrow-left: "\f060";
$fa-var-arrow-right: "\f061";
$fa-var-arrow-up: "\f062";
$fa-var-arrow-down: "\f063";
$fa-var-share: "\f064";
$fa-var-expand: "\f065";
$fa-var-compress: "\f066";
$fa-var-plus: "\f067";
$fa-var-minus: "\f068";
$fa-var-asterisk: "\f069";
$fa-var-exclamation-circle: "\f06a";
$fa-var-gift: "\f06b";
$fa-var-leaf: "\f06c";
$fa-var-fire: "\f06d";
$fa-var-eye: "\f06e";
$fa-var-eye-slash: "\f070";
$fa-var-exclamation-triangle: "\f071";
$fa-var-plane: "\f072";
$fa-var-calendar: "\f073";
$fa-var-random: "\f074";
$fa-var-comment: "\f075";
$fa-var-magnet: "\f076";
$fa-var-chevron-up: "\f077";
$fa-var-chevron-down: "\f078";
$fa-var-retweet: "\f079";
$fa-var-shopping-cart: "\f07a";
$fa-var-folder: "\f07b";
$fa-var-folder-open: "\f07c";
$fa-var-arrows-v: "\f07d";
$fa-var-arrows-h: "\f07e";
$fa-var-bar-chart-o: "\f080";
$fa-var-twitter-square: "\f081";
$fa-var-facebook-square: "\f082";
$fa-var-camera-retro: "\f083";
$fa-var-key: "\f084";
$fa-var-cogs: "\f085";
$fa-var-comments: "\f086";
$fa-var-thumbs-o-up: "\f087";
$fa-var-thumbs-o-down: "\f088";
$fa-var-star-half: "\f089";
$fa-var-heart-o: "\f08a";
$fa-var-sign-out: "\f08b";
$fa-var-linkedin-square: "\f08c";
$fa-var-thumb-tack: "\f08d";
$fa-var-external-link: "\f08e";
$fa-var-sign-in: "\f090";
$fa-var-trophy: "\f091";
$fa-var-github-square: "\f092";
$fa-var-upload: "\f093";
$fa-var-lemon-o: "\f094";
$fa-var-phone: "\f095";
$fa-var-square-o: "\f096";
$fa-var-bookmark-o: "\f097";
$fa-var-phone-square: "\f098";
$fa-var-twitter: "\f099";
$fa-var-facebook: "\f09a";
$fa-var-github: "\f09b";
$fa-var-unlock: "\f09c";
$fa-var-credit-card: "\f09d";
$fa-var-rss: "\f09e";
$fa-var-hdd-o: "\f0a0";
$fa-var-bullhorn: "\f0a1";
$fa-var-bell: "\f0f3";
$fa-var-certificate: "\f0a3";
$fa-var-hand-o-right: "\f0a4";
$fa-var-hand-o-left: "\f0a5";
$fa-var-hand-o-up: "\f0a6";
$fa-var-hand-o-down: "\f0a7";
$fa-var-arrow-circle-left: "\f0a8";
$fa-var-arrow-circle-right: "\f0a9";
$fa-var-arrow-circle-up: "\f0aa";
$fa-var-arrow-circle-down: "\f0ab";
$fa-var-globe: "\f0ac";
$fa-var-wrench: "\f0ad";
$fa-var-tasks: "\f0ae";
$fa-var-filter: "\f0b0";
$fa-var-briefcase: "\f0b1";
$fa-var-arrows-alt: "\f0b2";
$fa-var-users: "\f0c0";
$fa-var-link: "\f0c1";
$fa-var-cloud: "\f0c2";
$fa-var-flask: "\f0c3";
$fa-var-scissors: "\f0c4";
$fa-var-files-o: "\f0c5";
$fa-var-paperclip: "\f0c6";
$fa-var-floppy-o: "\f0c7";
$fa-var-square: "\f0c8";
$fa-var-bars: "\f0c9";
$fa-var-list-ul: "\f0ca";
$fa-var-list-ol: "\f0cb";
$fa-var-strikethrough: "\f0cc";
$fa-var-underline: "\f0cd";
$fa-var-table: "\f0ce";
$fa-var-magic: "\f0d0";
$fa-var-truck: "\f0d1";
$fa-var-pinterest: "\f0d2";
$fa-var-pinterest-square: "\f0d3";
$fa-var-google-plus-square: "\f0d4";
$fa-var-google-plus: "\f0d5";
$fa-var-money: "\f0d6";
$fa-var-caret-down: "\f0d7";
$fa-var-caret-up: "\f0d8";
$fa-var-caret-left: "\f0d9";
$fa-var-caret-right: "\f0da";
$fa-var-columns: "\f0db";
$fa-var-sort: "\f0dc";
$fa-var-sort-asc: "\f0dd";
$fa-var-sort-desc: "\f0de";
$fa-var-envelope: "\f0e0";
$fa-var-linkedin: "\f0e1";
$fa-var-undo: "\f0e2";
$fa-var-gavel: "\f0e3";
$fa-var-tachometer: "\f0e4";
$fa-var-comment-o: "\f0e5";
$fa-var-comments-o: "\f0e6";
$fa-var-bolt: "\f0e7";
$fa-var-sitemap: "\f0e8";
$fa-var-umbrella: "\f0e9";
$fa-var-clipboard: "\f0ea";
$fa-var-lightbulb-o: "\f0eb";
$fa-var-exchange: "\f0ec";
$fa-var-cloud-download: "\f0ed";
$fa-var-cloud-upload: "\f0ee";
$fa-var-user-md: "\f0f0";
$fa-var-stethoscope: "\f0f1";
$fa-var-suitcase: "\f0f2";
$fa-var-bell-o: "\f0a2";
$fa-var-coffee: "\f0f4";
$fa-var-cutlery: "\f0f5";
$fa-var-file-text-o: "\f0f6";
$fa-var-building-o: "\f0f7";
$fa-var-hospital-o: "\f0f8";
$fa-var-ambulance: "\f0f9";
$fa-var-medkit: "\f0fa";
$fa-var-fighter-jet: "\f0fb";
$fa-var-beer: "\f0fc";
$fa-var-h-square: "\f0fd";
$fa-var-plus-square: "\f0fe";
$fa-var-angle-double-left: "\f100";
$fa-var-angle-double-right: "\f101";
$fa-var-angle-double-up: "\f102";
$fa-var-angle-double-down: "\f103";
$fa-var-angle-left: "\f104";
$fa-var-angle-right: "\f105";
$fa-var-angle-up: "\f106";
$fa-var-angle-down: "\f107";
$fa-var-desktop: "\f108";
$fa-var-laptop: "\f109";
$fa-var-tablet: "\f10a";
$fa-var-mobile: "\f10b";
$fa-var-circle-o: "\f10c";
$fa-var-quote-left: "\f10d";
$fa-var-quote-right: "\f10e";
$fa-var-spinner: "\f110";
$fa-var-circle: "\f111";
$fa-var-reply: "\f112";
$fa-var-github-alt: "\f113";
$fa-var-folder-o: "\f114";
$fa-var-folder-open-o: "\f115";
$fa-var-smile-o: "\f118";
$fa-var-frown-o: "\f119";
$fa-var-meh-o: "\f11a";
$fa-var-gamepad: "\f11b";
$fa-var-keyboard-o: "\f11c";
$fa-var-flag-o: "\f11d";
$fa-var-flag-checkered: "\f11e";
$fa-var-terminal: "\f120";
$fa-var-code: "\f121";
$fa-var-reply-all: "\f122";
$fa-var-mail-reply-all: "\f122";
$fa-var-star-half-o: "\f123";
$fa-var-location-arrow: "\f124";
$fa-var-crop: "\f125";
$fa-var-code-fork: "\f126";
$fa-var-chain-broken: "\f127";
$fa-var-question: "\f128";
$fa-var-info: "\f129";
$fa-var-exclamation: "\f12a";
$fa-var-superscript: "\f12b";
$fa-var-subscript: "\f12c";
$fa-var-eraser: "\f12d";
$fa-var-puzzle-piece: "\f12e";
$fa-var-microphone: "\f130";
$fa-var-microphone-slash: "\f131";
$fa-var-shield: "\f132";
$fa-var-calendar-o: "\f133";
$fa-var-fire-extinguisher: "\f134";
$fa-var-rocket: "\f135";
$fa-var-maxcdn: "\f136";
$fa-var-chevron-circle-left: "\f137";
$fa-var-chevron-circle-right: "\f138";
$fa-var-chevron-circle-up: "\f139";
$fa-var-chevron-circle-down: "\f13a";
$fa-var-html5: "\f13b";
$fa-var-css3: "\f13c";
$fa-var-anchor: "\f13d";
$fa-var-unlock-alt: "\f13e";
$fa-var-bullseye: "\f140";
$fa-var-ellipsis-h: "\f141";
$fa-var-ellipsis-v: "\f142";
$fa-var-rss-square: "\f143";
$fa-var-play-circle: "\f144";
$fa-var-ticket: "\f145";
$fa-var-minus-square: "\f146";
$fa-var-minus-square-o: "\f147";
$fa-var-level-up: "\f148";
$fa-var-level-down: "\f149";
$fa-var-check-square: "\f14a";
$fa-var-pencil-square: "\f14b";
$fa-var-external-link-square: "\f14c";
$fa-var-share-square: "\f14d";
$fa-var-compass: "\f14e";
$fa-var-caret-square-o-down: "\f150";
$fa-var-caret-square-o-up: "\f151";
$fa-var-caret-square-o-right: "\f152";
$fa-var-eur: "\f153";
$fa-var-gbp: "\f154";
$fa-var-usd: "\f155";
$fa-var-inr: "\f156";
$fa-var-jpy: "\f157";
$fa-var-rub: "\f158";
$fa-var-krw: "\f159";
$fa-var-btc: "\f15a";
$fa-var-file: "\f15b";
$fa-var-file-text: "\f15c";
$fa-var-sort-alpha-asc: "\f15d";
$fa-var-sort-alpha-desc: "\f15e";
$fa-var-sort-amount-asc: "\f160";
$fa-var-sort-amount-desc: "\f161";
$fa-var-sort-numeric-asc: "\f162";
$fa-var-sort-numeric-desc: "\f163";
$fa-var-thumbs-up: "\f164";
$fa-var-thumbs-down: "\f165";
$fa-var-youtube-square: "\f166";
$fa-var-youtube: "\f167";
$fa-var-xing: "\f168";
$fa-var-xing-square: "\f169";
$fa-var-youtube-play: "\f16a";
$fa-var-dropbox: "\f16b";
$fa-var-stack-overflow: "\f16c";
$fa-var-instagram: "\f16d";
$fa-var-flickr: "\f16e";
$fa-var-adn: "\f170";
$fa-var-bitbucket: "\f171";
$fa-var-bitbucket-square: "\f172";
$fa-var-tumblr: "\f173";
$fa-var-tumblr-square: "\f174";
$fa-var-long-arrow-down: "\f175";
$fa-var-long-arrow-up: "\f176";
$fa-var-long-arrow-left: "\f177";
$fa-var-long-arrow-right: "\f178";
$fa-var-apple: "\f179";
$fa-var-windows: "\f17a";
$fa-var-android: "\f17b";
$fa-var-linux: "\f17c";
$fa-var-dribbble: "\f17d";
$fa-var-skype: "\f17e";
$fa-var-foursquare: "\f180";
$fa-var-trello: "\f181";
$fa-var-female: "\f182";
$fa-var-male: "\f183";
$fa-var-gittip: "\f184";
$fa-var-sun-o: "\f185";
$fa-var-moon-o: "\f186";
$fa-var-archive: "\f187";
$fa-var-bug: "\f188";
$fa-var-vk: "\f189";
$fa-var-weibo: "\f18a";
$fa-var-renren: "\f18b";
$fa-var-pagelines: "\f18c";
$fa-var-stack-exchange: "\f18d";
$fa-var-arrow-circle-o-right: "\f18e";
$fa-var-arrow-circle-o-left: "\f190";
$fa-var-caret-square-o-left: "\f191";
$fa-var-dot-circle-o: "\f192";
$fa-var-wheelchair: "\f193";
$fa-var-vimeo-square: "\f194";
$fa-var-try: "\f195";
$fa-var-plus-square-o: "\f196";